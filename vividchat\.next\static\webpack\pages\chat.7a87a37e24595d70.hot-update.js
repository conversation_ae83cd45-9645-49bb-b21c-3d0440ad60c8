"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/chat",{

/***/ "./src/components/Live2D.tsx":
/*!***********************************!*\
  !*** ./src/components/Live2D.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Live2D: function() { return /* binding */ Live2D; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_live2d_src_lappdelegate__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/live2d/src/lappdelegate */ \"./lib/live2d/src/lappdelegate.ts\");\n/* harmony import */ var _lib_live2d_src_lappdefine__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/live2d/src/lappdefine */ \"./lib/live2d/src/lappdefine.ts\");\n/* harmony import */ var _hooks_useLive2D__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../hooks/useLive2D */ \"./src/hooks/useLive2D.ts\");\n/* __next_internal_client_entry_do_not_use__ Live2D auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction Live2D() {\n    _s();\n    const { ready } = (0,_hooks_useLive2D__WEBPACK_IMPORTED_MODULE_4__.useLive2D)();\n    console.log(\"[Live2D] Component render, ready:\", ready);\n    const handleLoad = ()=>{\n        if (_lib_live2d_src_lappdelegate__WEBPACK_IMPORTED_MODULE_2__.LAppDelegate.getInstance().initialize() == false) {\n            return;\n        }\n        _lib_live2d_src_lappdelegate__WEBPACK_IMPORTED_MODULE_2__.LAppDelegate.getInstance().run();\n    };\n    const handleResize = ()=>{\n        if (_lib_live2d_src_lappdefine__WEBPACK_IMPORTED_MODULE_3__.CanvasSize === \"auto\") {\n            _lib_live2d_src_lappdelegate__WEBPACK_IMPORTED_MODULE_2__.LAppDelegate.getInstance().onResize();\n        }\n    };\n    const handleBeforeUnload = ()=>{\n        // Release instance\n        _lib_live2d_src_lappdelegate__WEBPACK_IMPORTED_MODULE_2__.LAppDelegate.releaseInstance();\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        handleLoad();\n        window.addEventListener(\"resize\", handleResize);\n        return ()=>{\n            window.removeEventListener(\"resize\", handleResize);\n            handleBeforeUnload();\n        };\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-4 right-4 w-80 h-80 z-0 rounded-lg overflow-hidden shadow-lg\",\n        children: [\n             false && /*#__PURE__*/ 0,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                id: \"live2dCanvas\",\n                className: \"w-full h-full bg-center bg-cover\",\n                style: {\n                    pointerEvents: \"none\"\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\vividchat\\\\src\\\\components\\\\Live2D.tsx\",\n                lineNumber: 49,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\vividchat\\\\src\\\\components\\\\Live2D.tsx\",\n        lineNumber: 41,\n        columnNumber: 9\n    }, this);\n}\n_s(Live2D, \"IG1pGKsyuClwKqrynpN+uzXccjA=\", false, function() {\n    return [\n        _hooks_useLive2D__WEBPACK_IMPORTED_MODULE_4__.useLive2D\n    ];\n});\n_c = Live2D;\nvar _c;\n$RefreshReg$(_c, \"Live2D\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Live2D.tsx\n"));

/***/ })

});