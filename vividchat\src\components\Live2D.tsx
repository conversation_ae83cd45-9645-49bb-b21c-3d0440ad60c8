'use client'

import React, { useEffect, useRef } from 'react';
import { LAppDelegate } from '@/lib/live2d/src/lappdelegate';
import * as LAppDefine from '@/lib/live2d/src/lappdefine';
import { useLive2D } from '../hooks/useLive2D';
import { useCharacterStore } from '../../lib/store/character';

export function Live2D() {
    const { ready, setLive2dCharacter } = useLive2D();
    const { character } = useCharacterStore();
    const currentCharacterRef = useRef<string | null>(null);
    const loadCountRef = useRef<number>(0);

    const handleLoad = () => {
        if (LAppDelegate.getInstance().initialize() == false) {
            return;
        }
        LAppDelegate.getInstance().run();

        // Load character from store
        if (character) {
            console.log('[Live2D] Loading initial character from store:', character);
            currentCharacterRef.current = character.resource_id;
            setLive2dCharacter(character);
        }
    }

    const handleResize = () => {
        if (LAppDefine.CanvasSize === 'auto') {
            LAppDelegate.getInstance().onResize();
        }
    }

    const handleBeforeUnload = () => {
        // Release instance
        LAppDelegate.releaseInstance();
    }

    useEffect(() => {
        handleLoad();
        window.addEventListener('resize', handleResize);
        return () => {
            window.removeEventListener('resize', handleResize);
            handleBeforeUnload();
        }
    }, []);

    // Listen for character changes
    useEffect(() => {
        if (character && character.resource_id !== currentCharacterRef.current) {
            loadCountRef.current += 1;
            console.log(`[Live2D] Character changed (load #${loadCountRef.current}), loading new character:`, character);
            console.log('[Live2D] Previous character:', currentCharacterRef.current);
            currentCharacterRef.current = character.resource_id;
            setLive2dCharacter(character);
        }
    }, [character]);

    return (
        <div className='fixed bottom-4 right-4 w-80 h-80 z-0 pointer-events-none'>
            {!ready && (
                <div className='absolute top-0 left-0 w-full h-full flex flex-row gap-2 items-center justify-center z-10 bg-white/10 dark:bg-gray-900/10 backdrop-blur-sm rounded-lg pointer-events-none'>
                    <p className='text-sm font-medium text-gray-700 dark:text-gray-300'>Loading Live2D...</p>
                    <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
                </div>
            )}
            <canvas
                id="live2dCanvas"
                className='w-full h-full pointer-events-none'
                style={{ pointerEvents: 'none' }}
            />
        </div>
    )
}
