'use client'

import React, { useEffect } from 'react';
import { LAppDelegate } from '@/lib/live2d/src/lappdelegate';
import * as LAppDefine from '@/lib/live2d/src/lappdefine';
import { useLive2D } from '../hooks/useLive2D';

export function Live2D() {
    const { ready } = useLive2D();

    console.log('[Live2D] Component render, ready:', ready);

    const handleLoad = () => {
        if (LAppDelegate.getInstance().initialize() == false) {
            return;
        }
        LAppDelegate.getInstance().run();
    }

    const handleResize = () => {
        if (LAppDefine.CanvasSize === 'auto') {
            LAppDelegate.getInstance().onResize();
        }
    }

    const handleBeforeUnload = () => {
        // Release instance
        LAppDelegate.releaseInstance();
    }

    useEffect(() => {
        handleLoad();
        window.addEventListener('resize', handleResize);
        return () => {
            window.removeEventListener('resize', handleResize);
            handleBeforeUnload();
        }
    }, []);

    return (
        <div className='fixed bottom-4 right-4 w-80 h-80 z-0 rounded-lg overflow-hidden shadow-lg'>
            {!ready && (
                <div className='absolute top-0 left-0 w-full h-full flex flex-row gap-1 items-center justify-center z-50'>
                    <p className='text-sm font-bold'>Loading Live2D...</p>
                    <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
                </div>
            )}
            <canvas
                id="live2dCanvas"
                className='w-full h-full bg-center bg-cover'
            />
        </div>
    )
}
