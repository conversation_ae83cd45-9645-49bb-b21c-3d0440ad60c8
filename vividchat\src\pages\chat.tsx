import { useRouter } from 'next/router';
import React, { useState, useEffect, useRef } from 'react';

import { Result } from '@/components/ChatDemo/result';
import { Search } from '@/components/ChatDemo/search';
import useSwitchManager from '@/components/ChatDemo/SwitchManager';
import Layout from '@/components/Layout';
import Sidebar from '@/components/Sidebar';
import { Live2D } from '@/components/Live2D';
import { CharacterSelector } from '@/components/CharacterSelector';
import { useUserContext } from '@/context/UserContext';
import { loadChatConfig } from '@/config/chatConfig';
import { Message } from '@/types';
import { safeJsonParse } from '@/lib/utils';
import { useLive2D } from '@/hooks/useLive2D';
import { useCharacterStore } from '../../lib/store/character';

interface Collection {
  id: string;
  name: string;
}

const ChatPage: React.FC = () => {
  const router = useRouter();
  const { isAuthenticated, getClient, selectedModel, pipeline } = useUserContext();
  const { setLive2dCharacter } = useLive2D();
  const { character } = useCharacterStore();

  const [query, setQuery] = useState('');
  const [hasAttemptedFetch, setHasAttemptedFetch] = useState(false);
  const [selectedCollectionIds, setSelectedCollectionIds] = useState<string[]>([]);
  const [selectedConversationId, setSelectedConversationId] = useState<string | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [mode] = useState<'rag' | 'rag_agent'>('rag_agent'); // Fixed to Agent Mode
  const [sidebarIsOpen, setSidebarIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [uploadedDocuments, setUploadedDocuments] = useState<string[]>([]);
  const [collections, setCollections] = useState<Collection[]>([]);
  const [userId, setUserId] = useState<string | null>(null);

  // Configuration state
  const [searchLimit, setSearchLimit] = useState<number>(10);
  const [searchFilters, setSearchFilters] = useState('{}');
  const [indexMeasure, setIndexMeasure] = useState<string>('cosine_distance');
  const [includeMetadatas, setIncludeMetadatas] = useState<boolean>(false);
  const [probes, setProbes] = useState<number>();
  const [efSearch, setEfSearch] = useState<number>();
  const [fullTextWeight, setFullTextWeight] = useState<number>();
  const [semanticWeight, setSemanticWeight] = useState<number>();
  const [fullTextLimit, setFullTextLimit] = useState<number>();
  const [rrfK, setRrfK] = useState<number>();
  const [kgSearchLevel, setKgSearchLevel] = useState<number | null>(null);
  const [maxCommunityDescriptionLength, setMaxCommunityDescriptionLength] = useState<number>(200);
  const [localSearchLimits, setLocalSearchLimits] = useState<Record<string, number>>({});
  const [temperature, setTemperature] = useState(0.1);
  const [topP, setTopP] = useState(1);
  const [topK, setTopK] = useState(100);
  const [maxTokensToSample, setMaxTokensToSample] = useState(1024);

  const contentAreaRef = useRef<HTMLDivElement>(null);
  const { switches, initializeSwitch, updateSwitch } = useSwitchManager();

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login');
    }
  }, [isAuthenticated, router]);

  // Load configuration and initialize switches
  useEffect(() => {
    const initializeApp = async () => {
      try {
        const config = await loadChatConfig();
        
        // Set configuration values
        setSearchLimit(config.vectorSearch.searchLimit);
        setSearchFilters(config.vectorSearch.searchFilters);
        setIndexMeasure(config.vectorSearch.indexMeasure);
        setIncludeMetadatas(config.vectorSearch.includeMetadatas);
        setProbes(config.vectorSearch.probes);
        setEfSearch(config.vectorSearch.efSearch);
        
        setFullTextWeight(config.hybridSearch.fullTextWeight);
        setSemanticWeight(config.hybridSearch.semanticWeight);
        setFullTextLimit(config.hybridSearch.fullTextLimit);
        setRrfK(config.hybridSearch.rrfK);
        
        setKgSearchLevel(config.graphSearch.kgSearchLevel);
        setMaxCommunityDescriptionLength(config.graphSearch.maxCommunityDescriptionLength);
        setLocalSearchLimits(config.graphSearch.localSearchLimits || {});
        
        setTemperature(config.ragGeneration.temperature);
        setTopP(config.ragGeneration.topP);
        setTopK(config.ragGeneration.topK);
        setMaxTokensToSample(config.ragGeneration.maxTokensToSample);

        // Initialize switches based on configuration
        initializeSwitch('vectorSearch', config.vectorSearch.enabled, 'Vector Search', 'Enable vector-based semantic search');
        initializeSwitch('hybridSearch', config.hybridSearch.enabled, 'Hybrid Search', 'Combine vector and full-text search');
        initializeSwitch('kgSearch', config.graphSearch.enabled, 'Knowledge Graph Search', 'Search using knowledge graph');
        initializeSwitch('includeMetadatas', config.vectorSearch.includeMetadatas, 'Include Metadata', 'Include document metadata in results');

        // Initialize Live2D character - always load the default character like livechat does
        console.log('[ChatPage] Loading initial character:', character);
        setLive2dCharacter(character);

      } catch (error) {
        console.error('Failed to load configuration:', error);
      } finally {
        setIsLoading(false);
      }
    };

    if (isAuthenticated) {
      initializeApp();
    }
  }, [isAuthenticated, initializeSwitch]);

  // Load collections
  useEffect(() => {
    const fetchCollections = async () => {
      try {
        const client = getClient();
        if (client) {
          const response = await client.collections.list();
          setCollections(response.results || []);
        }
      } catch (error) {
        console.error('Error fetching collections:', error);
      }
    };

    if (isAuthenticated && !isLoading) {
      fetchCollections();
    }
  }, [isAuthenticated, isLoading, getClient]);

  // Handle query from URL parameters
  useEffect(() => {
    if (router.query.q && typeof router.query.q === 'string') {
      setQuery(decodeURIComponent(router.query.q));
    }
  }, [router.query.q]);

  const toggleSidebar = () => {
    setSidebarIsOpen(!sidebarIsOpen);
  };

  const handleSwitchChange = (id: string, checked: boolean) => {
    updateSwitch(id, checked);
  };

  const handleAbortRequest = () => {
    // This will be handled by the Result component
    console.log('Abort request triggered');
  };

  const handleConversationSelect = async (conversationId: string) => {
    setSelectedConversationId(conversationId);
    try {
      const client = getClient();
      if (!client) {
        throw new Error('Failed to get authenticated client');
      }
      const response = await client.conversations.retrieve({
        id: conversationId,
      });
      const fetchedMessages = response.results.map((message: any) => ({
        id: message.id,
        role: message.metadata?.role || 'user',
        content: message.metadata?.content || '',
        timestamp: message.metadata?.timestamp || Date.now(),
      }));
      setMessages(fetchedMessages);
    } catch (error) {
      console.error('Error fetching conversation:', error);
    }
  };

  if (!isAuthenticated) {
    return null; // Will redirect to login
  }

  return (
    <Layout pageTitle="Chat" includeFooter={false}>
      <div className="flex h-[calc(100vh-4rem)] overflow-hidden">
        <Sidebar
          isOpen={sidebarIsOpen}
          onToggle={toggleSidebar}
          switches={switches}
          handleSwitchChange={handleSwitchChange}
          searchLimit={searchLimit}
          setSearchLimit={setSearchLimit}
          searchFilters={searchFilters}
          setSearchFilters={setSearchFilters}
          collections={collections}
          selectedCollectionIds={selectedCollectionIds}
          setSelectedCollectionIds={setSelectedCollectionIds}
          indexMeasure={indexMeasure}
          setIndexMeasure={setIndexMeasure}
          includeMetadatas={includeMetadatas}
          setIncludeMetadatas={setIncludeMetadatas}
          probes={probes}
          setProbes={setProbes}
          efSearch={efSearch}
          setEfSearch={setEfSearch}
          fullTextWeight={fullTextWeight}
          setFullTextWeight={setFullTextWeight}
          semanticWeight={semanticWeight}
          setSemanticWeight={setSemanticWeight}
          fullTextLimit={fullTextLimit}
          setFullTextLimit={setFullTextLimit}
          rrfK={rrfK}
          setRrfK={setRrfK}
          kgSearchLevel={kgSearchLevel}
          setKgSearchLevel={setKgSearchLevel}
          maxCommunityDescriptionLength={maxCommunityDescriptionLength}
          setMaxCommunityDescriptionLength={setMaxCommunityDescriptionLength}
          localSearchLimits={localSearchLimits}
          setLocalSearchLimits={setLocalSearchLimits}
          temperature={temperature}
          setTemperature={setTemperature}
          topP={topP}
          setTopP={setTopP}
          topK={topK}
          setTopK={setTopK}
          maxTokensToSample={maxTokensToSample}
          setMaxTokensToSample={setMaxTokensToSample}
          onConversationSelect={handleConversationSelect}
        />

        {/* Main Content */}
        <div className={`flex-1 flex flex-col transition-all duration-300 ${sidebarIsOpen ? 'ml-80' : 'ml-0'}`}>
          <div className="flex-1 flex flex-col overflow-hidden" ref={contentAreaRef}>
            <div className="flex-1 flex justify-center overflow-hidden">
              <div className="w-full max-w-4xl flex flex-col overflow-hidden">
                {/* Chat Interface */}
                <div className="flex-1 overflow-auto p-4">
              <Result
                query={query}
                setQuery={setQuery}
                model={selectedModel}
                userId={userId}
                pipelineUrl={pipeline?.deploymentUrl || ''}
                searchLimit={searchLimit}
                searchFilters={safeJsonParse(searchFilters)}
                ragTemperature={temperature}
                ragTopP={topP}
                ragTopK={topK}
                ragMaxTokensToSample={maxTokensToSample}
                uploadedDocuments={uploadedDocuments}
                setUploadedDocuments={setUploadedDocuments}
                switches={switches}
                hasAttemptedFetch={hasAttemptedFetch}
                mode={mode}
                selectedCollectionIds={selectedCollectionIds}
                onAbortRequest={handleAbortRequest}
                messages={messages}
                setMessages={setMessages}
                selectedConversationId={selectedConversationId}
                setSelectedConversationId={setSelectedConversationId}
                getClient={getClient}
              />
                </div>

                {/* Search Bar */}
                <div className="p-4 border-t border-border">
                  <Search
                    pipeline={pipeline || undefined}
                    setQuery={setQuery}
                    placeholder="Start a conversation..."
                    disabled={false}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Live2D Character */}
        <Live2D />
      </div>
    </Layout>
  );
};

export default ChatPage;
