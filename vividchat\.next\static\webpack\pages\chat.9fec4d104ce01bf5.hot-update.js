"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/chat",{

/***/ "./lib/live2d/src/lappmodel.ts":
/*!*************************************!*\
  !*** ./lib/live2d/src/lappmodel.ts ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LAppModel: function() { return /* binding */ LAppModel; }\n/* harmony export */ });\n/* harmony import */ var _framework_cubismdefaultparameterid__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @framework/cubismdefaultparameterid */ \"./lib/live2d/Framework/src/cubismdefaultparameterid.ts\");\n/* harmony import */ var _framework_cubismmodelsettingjson__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @framework/cubismmodelsettingjson */ \"./lib/live2d/Framework/src/cubismmodelsettingjson.ts\");\n/* harmony import */ var _framework_effect_cubismbreath__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @framework/effect/cubismbreath */ \"./lib/live2d/Framework/src/effect/cubismbreath.ts\");\n/* harmony import */ var _framework_effect_cubismeyeblink__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @framework/effect/cubismeyeblink */ \"./lib/live2d/Framework/src/effect/cubismeyeblink.ts\");\n/* harmony import */ var _framework_live2dcubismframework__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @framework/live2dcubismframework */ \"./lib/live2d/Framework/src/live2dcubismframework.ts\");\n/* harmony import */ var _framework_model_cubismusermodel__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @framework/model/cubismusermodel */ \"./lib/live2d/Framework/src/model/cubismusermodel.ts\");\n/* harmony import */ var _framework_motion_acubismmotion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @framework/motion/acubismmotion */ \"./lib/live2d/Framework/src/motion/acubismmotion.ts\");\n/* harmony import */ var _framework_motion_cubismmotionqueuemanager__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @framework/motion/cubismmotionqueuemanager */ \"./lib/live2d/Framework/src/motion/cubismmotionqueuemanager.ts\");\n/* harmony import */ var _framework_type_csmmap__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @framework/type/csmmap */ \"./lib/live2d/Framework/src/type/csmmap.ts\");\n/* harmony import */ var _framework_type_csmvector__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @framework/type/csmvector */ \"./lib/live2d/Framework/src/type/csmvector.ts\");\n/* harmony import */ var _framework_utils_cubismdebug__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @framework/utils/cubismdebug */ \"./lib/live2d/Framework/src/utils/cubismdebug.ts\");\n/* harmony import */ var _lappdefine__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./lappdefine */ \"./lib/live2d/src/lappdefine.ts\");\n/* harmony import */ var _lapppal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./lapppal */ \"./lib/live2d/src/lapppal.ts\");\n/* harmony import */ var _lappwavfilehandler__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./lappwavfilehandler */ \"./lib/live2d/src/lappwavfilehandler.ts\");\n/* harmony import */ var _framework_model_cubismmoc__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @framework/model/cubismmoc */ \"./lib/live2d/Framework/src/model/cubismmoc.ts\");\n/* harmony import */ var _live2dManager__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../live2dManager */ \"./lib/live2d/live2dManager.ts\");\n/**\r\n * Copyright(c) Live2D Inc. All rights reserved.\r\n *\r\n * Use of this source code is governed by the Live2D Open Software license\r\n * that can be found at https://www.live2d.com/eula/live2d-open-software-license-agreement_en.html.\r\n */ \n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar LoadStep;\n(function(LoadStep) {\n    LoadStep[LoadStep[\"LoadAssets\"] = 0] = \"LoadAssets\";\n    LoadStep[LoadStep[\"LoadModel\"] = 1] = \"LoadModel\";\n    LoadStep[LoadStep[\"WaitLoadModel\"] = 2] = \"WaitLoadModel\";\n    LoadStep[LoadStep[\"LoadExpression\"] = 3] = \"LoadExpression\";\n    LoadStep[LoadStep[\"WaitLoadExpression\"] = 4] = \"WaitLoadExpression\";\n    LoadStep[LoadStep[\"LoadPhysics\"] = 5] = \"LoadPhysics\";\n    LoadStep[LoadStep[\"WaitLoadPhysics\"] = 6] = \"WaitLoadPhysics\";\n    LoadStep[LoadStep[\"LoadPose\"] = 7] = \"LoadPose\";\n    LoadStep[LoadStep[\"WaitLoadPose\"] = 8] = \"WaitLoadPose\";\n    LoadStep[LoadStep[\"SetupEyeBlink\"] = 9] = \"SetupEyeBlink\";\n    LoadStep[LoadStep[\"SetupBreath\"] = 10] = \"SetupBreath\";\n    LoadStep[LoadStep[\"LoadUserData\"] = 11] = \"LoadUserData\";\n    LoadStep[LoadStep[\"WaitLoadUserData\"] = 12] = \"WaitLoadUserData\";\n    LoadStep[LoadStep[\"SetupEyeBlinkIds\"] = 13] = \"SetupEyeBlinkIds\";\n    LoadStep[LoadStep[\"SetupLipSyncIds\"] = 14] = \"SetupLipSyncIds\";\n    LoadStep[LoadStep[\"SetupLayout\"] = 15] = \"SetupLayout\";\n    LoadStep[LoadStep[\"LoadMotion\"] = 16] = \"LoadMotion\";\n    LoadStep[LoadStep[\"WaitLoadMotion\"] = 17] = \"WaitLoadMotion\";\n    LoadStep[LoadStep[\"CompleteInitialize\"] = 18] = \"CompleteInitialize\";\n    LoadStep[LoadStep[\"CompleteSetupModel\"] = 19] = \"CompleteSetupModel\";\n    LoadStep[LoadStep[\"LoadTexture\"] = 20] = \"LoadTexture\";\n    LoadStep[LoadStep[\"WaitLoadTexture\"] = 21] = \"WaitLoadTexture\";\n    LoadStep[LoadStep[\"CompleteSetup\"] = 22] = \"CompleteSetup\";\n})(LoadStep || (LoadStep = {}));\n/**\r\n * ユーザーが実際に使用するモデルの実装クラス<br>\r\n * モデル生成、機能コンポーネント生成、更新処理とレンダリングの呼び出しを行う。\r\n */ class LAppModel extends _framework_model_cubismusermodel__WEBPACK_IMPORTED_MODULE_5__.CubismUserModel {\n    /**\r\n   * model3.jsonが置かれたディレクトリとファイルパスからモデルを生成する\r\n   * @param dir\r\n   * @param fileName\r\n   */ loadAssets(dir, fileName) {\n        this._modelHomeDir = dir;\n        _live2dManager__WEBPACK_IMPORTED_MODULE_15__.Live2dManager.getInstance().setReady(false);\n        fetch(\"\".concat(this._modelHomeDir).concat(fileName)).then((response)=>response.arrayBuffer()).then((arrayBuffer)=>{\n            const setting = new _framework_cubismmodelsettingjson__WEBPACK_IMPORTED_MODULE_1__.CubismModelSettingJson(arrayBuffer, arrayBuffer.byteLength);\n            // ステートを更新\n            this._state = 1;\n            // 結果を保存\n            this.setupModel(setting);\n        }).catch((error)=>{\n            // model3.json読み込みでエラーが発生した時点で描画は不可能なので、setupせずエラーをcatchして何もしない\n            (0,_framework_utils_cubismdebug__WEBPACK_IMPORTED_MODULE_10__.CubismLogError)(\"Failed to load file \".concat(this._modelHomeDir).concat(fileName));\n        });\n    }\n    /**\r\n   * model3.jsonからモデルを生成する。\r\n   * model3.jsonの記述に従ってモデル生成、モーション、物理演算などのコンポーネント生成を行う。\r\n   *\r\n   * @param setting ICubismModelSettingのインスタンス\r\n   */ setupModel(setting) {\n        this._updating = true;\n        this._initialized = false;\n        this._modelSetting = setting;\n        // CubismModel\n        if (this._modelSetting.getModelFileName() != \"\") {\n            const modelFileName = this._modelSetting.getModelFileName();\n            fetch(\"\".concat(this._modelHomeDir).concat(modelFileName)).then((response)=>{\n                if (response.ok) {\n                    return response.arrayBuffer();\n                } else if (response.status >= 400) {\n                    (0,_framework_utils_cubismdebug__WEBPACK_IMPORTED_MODULE_10__.CubismLogError)(\"Failed to load file \".concat(this._modelHomeDir).concat(modelFileName));\n                    return new ArrayBuffer(0);\n                }\n            }).then((arrayBuffer)=>{\n                this.loadModel(arrayBuffer, this._mocConsistency);\n                this._state = 3;\n                // callback\n                loadCubismExpression();\n            });\n            this._state = 2;\n        } else {\n            _lapppal__WEBPACK_IMPORTED_MODULE_12__.LAppPal.printMessage(\"Model data does not exist.\");\n        }\n        // Expression\n        const loadCubismExpression = ()=>{\n            if (this._modelSetting.getExpressionCount() > 0) {\n                const count = this._modelSetting.getExpressionCount();\n                for(let i = 0; i < count; i++){\n                    const expressionName = this._modelSetting.getExpressionName(i);\n                    const expressionFileName = this._modelSetting.getExpressionFileName(i);\n                    fetch(\"\".concat(this._modelHomeDir).concat(expressionFileName)).then((response)=>{\n                        if (response.ok) {\n                            return response.arrayBuffer();\n                        } else if (response.status >= 400) {\n                            (0,_framework_utils_cubismdebug__WEBPACK_IMPORTED_MODULE_10__.CubismLogError)(\"Failed to load file \".concat(this._modelHomeDir).concat(expressionFileName));\n                            // ファイルが存在しなくてもresponseはnullを返却しないため、空のArrayBufferで対応する\n                            return new ArrayBuffer(0);\n                        }\n                    }).then((arrayBuffer)=>{\n                        const motion = this.loadExpression(arrayBuffer, arrayBuffer.byteLength, expressionName);\n                        if (this._expressions.getValue(expressionName) != null) {\n                            _framework_motion_acubismmotion__WEBPACK_IMPORTED_MODULE_6__.ACubismMotion.delete(this._expressions.getValue(expressionName));\n                            this._expressions.setValue(expressionName, null);\n                        }\n                        this._expressions.setValue(expressionName, motion);\n                        this._expressionCount++;\n                        if (this._expressionCount >= count) {\n                            this._state = 5;\n                            // callback\n                            loadCubismPhysics();\n                        }\n                    });\n                }\n                this._state = 4;\n            } else {\n                this._state = 5;\n                // callback\n                loadCubismPhysics();\n            }\n        };\n        // Physics\n        const loadCubismPhysics = ()=>{\n            if (this._modelSetting.getPhysicsFileName() != \"\") {\n                const physicsFileName = this._modelSetting.getPhysicsFileName();\n                fetch(\"\".concat(this._modelHomeDir).concat(physicsFileName)).then((response)=>{\n                    if (response.ok) {\n                        return response.arrayBuffer();\n                    } else if (response.status >= 400) {\n                        (0,_framework_utils_cubismdebug__WEBPACK_IMPORTED_MODULE_10__.CubismLogError)(\"Failed to load file \".concat(this._modelHomeDir).concat(physicsFileName));\n                        return new ArrayBuffer(0);\n                    }\n                }).then((arrayBuffer)=>{\n                    this.loadPhysics(arrayBuffer, arrayBuffer.byteLength);\n                    this._state = 7;\n                    // callback\n                    loadCubismPose();\n                });\n                this._state = 6;\n            } else {\n                this._state = 7;\n                // callback\n                loadCubismPose();\n            }\n        };\n        // Pose\n        const loadCubismPose = ()=>{\n            if (this._modelSetting.getPoseFileName() != \"\") {\n                const poseFileName = this._modelSetting.getPoseFileName();\n                fetch(\"\".concat(this._modelHomeDir).concat(poseFileName)).then((response)=>{\n                    if (response.ok) {\n                        return response.arrayBuffer();\n                    } else if (response.status >= 400) {\n                        (0,_framework_utils_cubismdebug__WEBPACK_IMPORTED_MODULE_10__.CubismLogError)(\"Failed to load file \".concat(this._modelHomeDir).concat(poseFileName));\n                        return new ArrayBuffer(0);\n                    }\n                }).then((arrayBuffer)=>{\n                    this.loadPose(arrayBuffer, arrayBuffer.byteLength);\n                    this._state = 9;\n                    // callback\n                    setupEyeBlink();\n                });\n                this._state = 8;\n            } else {\n                this._state = 9;\n                // callback\n                setupEyeBlink();\n            }\n        };\n        // EyeBlink\n        const setupEyeBlink = ()=>{\n            if (this._modelSetting.getEyeBlinkParameterCount() > 0) {\n                this._eyeBlink = _framework_effect_cubismeyeblink__WEBPACK_IMPORTED_MODULE_3__.CubismEyeBlink.create(this._modelSetting);\n                this._state = 10;\n            }\n            // callback\n            setupBreath();\n        };\n        // Breath\n        const setupBreath = ()=>{\n            this._breath = _framework_effect_cubismbreath__WEBPACK_IMPORTED_MODULE_2__.CubismBreath.create();\n            const breathParameters = new _framework_type_csmvector__WEBPACK_IMPORTED_MODULE_9__.csmVector();\n            breathParameters.pushBack(new _framework_effect_cubismbreath__WEBPACK_IMPORTED_MODULE_2__.BreathParameterData(this._idParamAngleX, 0.0, 15.0, 6.5345, 0.5));\n            breathParameters.pushBack(new _framework_effect_cubismbreath__WEBPACK_IMPORTED_MODULE_2__.BreathParameterData(this._idParamAngleY, 0.0, 8.0, 3.5345, 0.5));\n            breathParameters.pushBack(new _framework_effect_cubismbreath__WEBPACK_IMPORTED_MODULE_2__.BreathParameterData(this._idParamAngleZ, 0.0, 10.0, 5.5345, 0.5));\n            breathParameters.pushBack(new _framework_effect_cubismbreath__WEBPACK_IMPORTED_MODULE_2__.BreathParameterData(this._idParamBodyAngleX, 0.0, 4.0, 15.5345, 0.5));\n            breathParameters.pushBack(new _framework_effect_cubismbreath__WEBPACK_IMPORTED_MODULE_2__.BreathParameterData(_framework_live2dcubismframework__WEBPACK_IMPORTED_MODULE_4__.CubismFramework.getIdManager().getId(_framework_cubismdefaultparameterid__WEBPACK_IMPORTED_MODULE_0__.CubismDefaultParameterId.ParamBreath), 0.5, 0.5, 3.2345, 1));\n            this._breath.setParameters(breathParameters);\n            this._state = 11;\n            // callback\n            loadUserData();\n        };\n        // UserData\n        const loadUserData = ()=>{\n            if (this._modelSetting.getUserDataFile() != \"\") {\n                const userDataFile = this._modelSetting.getUserDataFile();\n                fetch(\"\".concat(this._modelHomeDir).concat(userDataFile)).then((response)=>{\n                    if (response.ok) {\n                        return response.arrayBuffer();\n                    } else if (response.status >= 400) {\n                        (0,_framework_utils_cubismdebug__WEBPACK_IMPORTED_MODULE_10__.CubismLogError)(\"Failed to load file \".concat(this._modelHomeDir).concat(userDataFile));\n                        return new ArrayBuffer(0);\n                    }\n                }).then((arrayBuffer)=>{\n                    this.loadUserData(arrayBuffer, arrayBuffer.byteLength);\n                    this._state = 13;\n                    // callback\n                    setupEyeBlinkIds();\n                });\n                this._state = 12;\n            } else {\n                this._state = 13;\n                // callback\n                setupEyeBlinkIds();\n            }\n        };\n        // EyeBlinkIds\n        const setupEyeBlinkIds = ()=>{\n            const eyeBlinkIdCount = this._modelSetting.getEyeBlinkParameterCount();\n            for(let i = 0; i < eyeBlinkIdCount; ++i){\n                this._eyeBlinkIds.pushBack(this._modelSetting.getEyeBlinkParameterId(i));\n            }\n            this._state = 14;\n            // callback\n            setupLipSyncIds();\n        };\n        // LipSyncIds\n        const setupLipSyncIds = ()=>{\n            const lipSyncIdCount = this._modelSetting.getLipSyncParameterCount();\n            for(let i = 0; i < lipSyncIdCount; ++i){\n                this._lipSyncIds.pushBack(this._modelSetting.getLipSyncParameterId(i));\n            }\n            this._state = 15;\n            // callback\n            setupLayout();\n        };\n        // Layout\n        const setupLayout = ()=>{\n            const layout = new _framework_type_csmmap__WEBPACK_IMPORTED_MODULE_8__.csmMap();\n            if (this._modelSetting == null || this._modelMatrix == null) {\n                (0,_framework_utils_cubismdebug__WEBPACK_IMPORTED_MODULE_10__.CubismLogError)(\"Failed to setupLayout().\");\n                return;\n            }\n            this._modelSetting.getLayoutMap(layout);\n            this._modelMatrix.setupFromLayout(layout);\n            this._state = 16;\n            // callback\n            loadCubismMotion();\n        };\n        // Motion\n        const loadCubismMotion = ()=>{\n            this._state = 17;\n            this._model.saveParameters();\n            this._allMotionCount = 0;\n            this._motionCount = 0;\n            const group = [];\n            const motionGroupCount = this._modelSetting.getMotionGroupCount();\n            // モーションの総数を求める\n            for(let i = 0; i < motionGroupCount; i++){\n                group[i] = this._modelSetting.getMotionGroupName(i);\n                this._allMotionCount += this._modelSetting.getMotionCount(group[i]);\n            }\n            // モーションの読み込み\n            for(let i = 0; i < motionGroupCount; i++){\n                this.preLoadMotionGroup(group[i]);\n            }\n            // モーションがない場合\n            if (motionGroupCount == 0) {\n                this._state = 20;\n                // 全てのモーションを停止する\n                this._motionManager.stopAllMotions();\n                this._updating = false;\n                this._initialized = true;\n                this.createRenderer();\n                this.setupTextures();\n                this.getRenderer().startUp(this._subdelegate.getGlManager().getGl());\n            }\n        };\n    }\n    /**\r\n   * テクスチャユニットにテクスチャをロードする\r\n   */ setupTextures() {\n        // iPhoneでのアルファ品質向上のためTypescriptではpremultipliedAlphaを採用\n        const usePremultiply = true;\n        if (this._state == 20) {\n            // テクスチャ読み込み用\n            const textureCount = this._modelSetting.getTextureCount();\n            for(let modelTextureNumber = 0; modelTextureNumber < textureCount; modelTextureNumber++){\n                // テクスチャ名が空文字だった場合はロード・バインド処理をスキップ\n                if (this._modelSetting.getTextureFileName(modelTextureNumber) == \"\") {\n                    continue;\n                }\n                // WebGLのテクスチャユニットにテクスチャをロードする\n                let texturePath = this._modelSetting.getTextureFileName(modelTextureNumber);\n                texturePath = this._modelHomeDir + texturePath;\n                // ロード完了時に呼び出すコールバック関数\n                const onLoad = (textureInfo)=>{\n                    this.getRenderer().bindTexture(modelTextureNumber, textureInfo.id);\n                    this._textureCount++;\n                    if (this._textureCount >= textureCount) {\n                        // ロード完了\n                        this._state = 22;\n                        // 全てのテクスチャが読み込み完了した時点でreadyを設定\n                        _live2dManager__WEBPACK_IMPORTED_MODULE_15__.Live2dManager.getInstance().setReady(true);\n                    }\n                };\n                // 読み込み\n                this._subdelegate.getTextureManager().createTextureFromPngFile(texturePath, usePremultiply, onLoad);\n                this.getRenderer().setIsPremultipliedAlpha(usePremultiply);\n            }\n            this._state = 21;\n        }\n        _live2dManager__WEBPACK_IMPORTED_MODULE_15__.Live2dManager.getInstance().setReady(true);\n    }\n    /**\r\n   * レンダラを再構築する\r\n   */ reloadRenderer() {\n        this.deleteRenderer();\n        this.createRenderer();\n        this.setupTextures();\n    }\n    /**\r\n   * 更新\r\n   */ update() {\n        if (this._state != 22) return;\n        const deltaTimeSeconds = _lapppal__WEBPACK_IMPORTED_MODULE_12__.LAppPal.getDeltaTime();\n        this._userTimeSeconds += deltaTimeSeconds;\n        this._dragManager.update(deltaTimeSeconds);\n        this._dragX = this._dragManager.getX();\n        this._dragY = this._dragManager.getY();\n        // モーションによるパラメータ更新の有無\n        let motionUpdated = false;\n        //--------------------------------------------------------------------------\n        this._model.loadParameters(); // 前回セーブされた状態をロード\n        if (this._motionManager.isFinished()) {\n            // モーションの再生がない場合、待機モーションの中からランダムで再生する\n            this.startRandomMotion(_lappdefine__WEBPACK_IMPORTED_MODULE_11__.MotionGroupIdle, _lappdefine__WEBPACK_IMPORTED_MODULE_11__.PriorityIdle);\n        } else {\n            motionUpdated = this._motionManager.updateMotion(this._model, deltaTimeSeconds); // モーションを更新\n        }\n        this._model.saveParameters(); // 状態を保存\n        //--------------------------------------------------------------------------\n        // まばたき\n        if (!motionUpdated) {\n            if (this._eyeBlink != null) {\n                // メインモーションの更新がないとき\n                this._eyeBlink.updateParameters(this._model, deltaTimeSeconds); // 目パチ\n            }\n        }\n        if (this._expressionManager != null) {\n            this._expressionManager.updateMotion(this._model, deltaTimeSeconds); // 表情でパラメータ更新（相対変化）\n        }\n        // ドラッグによる変化\n        // ドラッグによる顔の向きの調整\n        this._model.addParameterValueById(this._idParamAngleX, this._dragX * 30); // -30から30の値を加える\n        this._model.addParameterValueById(this._idParamAngleY, this._dragY * 30);\n        this._model.addParameterValueById(this._idParamAngleZ, this._dragX * this._dragY * -30);\n        // ドラッグによる体の向きの調整\n        this._model.addParameterValueById(this._idParamBodyAngleX, this._dragX * 10); // -10から10の値を加える\n        // ドラッグによる目の向きの調整\n        this._model.addParameterValueById(this._idParamEyeBallX, this._dragX); // -1から1の値を加える\n        this._model.addParameterValueById(this._idParamEyeBallY, this._dragY);\n        // 呼吸など\n        if (this._breath != null) {\n            this._breath.updateParameters(this._model, deltaTimeSeconds);\n        }\n        // 物理演算の設定\n        if (this._physics != null) {\n            this._physics.evaluate(this._model, deltaTimeSeconds);\n        }\n        // リップシンクの設定\n        if (this._lipsync) {\n            let value = 0.0;\n            this._wavFileHandler.update(deltaTimeSeconds);\n            value = this._wavFileHandler.getRms() * _live2dManager__WEBPACK_IMPORTED_MODULE_15__.Live2dManager.getInstance().getLipFactor();\n            // 解析新的音频数据\n            let audioData = _live2dManager__WEBPACK_IMPORTED_MODULE_15__.Live2dManager.getInstance().playAudio();\n            if (audioData != null) {\n                this._wavFileHandler.start(audioData);\n            }\n            // 同步声音驱动口型\n            if (!_live2dManager__WEBPACK_IMPORTED_MODULE_15__.Live2dManager.getInstance().isAudioPlaying()) {\n                value = 0.0;\n            }\n            for(let i = 0; i < this._lipSyncIds.getSize(); ++i){\n                this._model.addParameterValueById(this._lipSyncIds.at(i), value, 0.8);\n            }\n        }\n        // ポーズの設定\n        if (this._pose != null) {\n            this._pose.updateParameters(this._model, deltaTimeSeconds);\n        }\n        this._model.update();\n    }\n    /**\r\n   * 引数で指定したモーションの再生を開始する\r\n   * @param group モーショングループ名\r\n   * @param no グループ内の番号\r\n   * @param priority 優先度\r\n   * @param onFinishedMotionHandler モーション再生終了時に呼び出されるコールバック関数\r\n   * @return 開始したモーションの識別番号を返す。個別のモーションが終了したか否かを判定するisFinished()の引数で使用する。開始できない時は[-1]\r\n   */ startMotion(group, no, priority, onFinishedMotionHandler, onBeganMotionHandler) {\n        if (priority == _lappdefine__WEBPACK_IMPORTED_MODULE_11__.PriorityForce) {\n            this._motionManager.setReservePriority(priority);\n        } else if (!this._motionManager.reserveMotion(priority)) {\n            if (this._debugMode) {\n                _lapppal__WEBPACK_IMPORTED_MODULE_12__.LAppPal.printMessage(\"[APP]can't start motion.\");\n            }\n            return _framework_motion_cubismmotionqueuemanager__WEBPACK_IMPORTED_MODULE_7__.InvalidMotionQueueEntryHandleValue;\n        }\n        const motionFileName = this._modelSetting.getMotionFileName(group, no);\n        // ex) idle_0\n        const name = \"\".concat(group, \"_\").concat(no);\n        let motion = this._motions.getValue(name);\n        let autoDelete = false;\n        if (motion == null) {\n            fetch(\"\".concat(this._modelHomeDir).concat(motionFileName)).then((response)=>{\n                if (response.ok) {\n                    return response.arrayBuffer();\n                } else if (response.status >= 400) {\n                    (0,_framework_utils_cubismdebug__WEBPACK_IMPORTED_MODULE_10__.CubismLogError)(\"Failed to load file \".concat(this._modelHomeDir).concat(motionFileName));\n                    return new ArrayBuffer(0);\n                }\n            }).then((arrayBuffer)=>{\n                motion = this.loadMotion(arrayBuffer, arrayBuffer.byteLength, null, onFinishedMotionHandler, onBeganMotionHandler, this._modelSetting, group, no);\n                if (motion == null) {\n                    return;\n                }\n                motion.setEffectIds(this._eyeBlinkIds, this._lipSyncIds);\n                autoDelete = true; // 終了時にメモリから削除\n            });\n        } else {\n            motion.setBeganMotionHandler(onBeganMotionHandler);\n            motion.setFinishedMotionHandler(onFinishedMotionHandler);\n        }\n        //voice\n        // const voice = this._modelSetting.getMotionSoundFileName(group, no);\n        // if (voice.localeCompare('') != 0) {\n        //   let path = voice;\n        //   path = this._modelHomeDir + path;\n        //   this._wavFileHandler.start(path);\n        // }\n        if (this._debugMode) {\n            _lapppal__WEBPACK_IMPORTED_MODULE_12__.LAppPal.printMessage(\"[APP]start motion: [\".concat(group, \"_\").concat(no));\n        }\n        return this._motionManager.startMotionPriority(motion, autoDelete, priority);\n    }\n    /**\r\n   * ランダムに選ばれたモーションの再生を開始する。\r\n   * @param group モーショングループ名\r\n   * @param priority 優先度\r\n   * @param onFinishedMotionHandler モーション再生終了時に呼び出されるコールバック関数\r\n   * @return 開始したモーションの識別番号を返す。個別のモーションが終了したか否かを判定するisFinished()の引数で使用する。開始できない時は[-1]\r\n   */ startRandomMotion(group, priority, onFinishedMotionHandler, onBeganMotionHandler) {\n        if (this._modelSetting.getMotionCount(group) == 0) {\n            return _framework_motion_cubismmotionqueuemanager__WEBPACK_IMPORTED_MODULE_7__.InvalidMotionQueueEntryHandleValue;\n        }\n        const no = Math.floor(Math.random() * this._modelSetting.getMotionCount(group));\n        return this.startMotion(group, no, priority, onFinishedMotionHandler, onBeganMotionHandler);\n    }\n    /**\r\n   * 引数で指定した表情モーションをセットする\r\n   *\r\n   * @param expressionId 表情モーションのID\r\n   */ setExpression(expressionId) {\n        const motion = this._expressions.getValue(expressionId);\n        if (this._debugMode) {\n            _lapppal__WEBPACK_IMPORTED_MODULE_12__.LAppPal.printMessage(\"[APP]expression: [\".concat(expressionId, \"]\"));\n        }\n        if (motion != null) {\n            this._expressionManager.startMotion(motion, false);\n        } else {\n            if (this._debugMode) {\n                _lapppal__WEBPACK_IMPORTED_MODULE_12__.LAppPal.printMessage(\"[APP]expression[\".concat(expressionId, \"] is null\"));\n            }\n        }\n    }\n    /**\r\n   * ランダムに選ばれた表情モーションをセットする\r\n   */ setRandomExpression() {\n        if (this._expressions.getSize() == 0) {\n            return;\n        }\n        const no = Math.floor(Math.random() * this._expressions.getSize());\n        for(let i = 0; i < this._expressions.getSize(); i++){\n            if (i == no) {\n                const name = this._expressions._keyValues[i].first;\n                this.setExpression(name);\n                return;\n            }\n        }\n    }\n    /**\r\n   * イベントの発火を受け取る\r\n   */ motionEventFired(eventValue) {\n        (0,_framework_utils_cubismdebug__WEBPACK_IMPORTED_MODULE_10__.CubismLogInfo)(\"{0} is fired on LAppModel!!\", eventValue.s);\n    }\n    /**\r\n   * 当たり判定テスト\r\n   * 指定ＩＤの頂点リストから矩形を計算し、座標をが矩形範囲内か判定する。\r\n   *\r\n   * @param hitArenaName  当たり判定をテストする対象のID\r\n   * @param x             判定を行うX座標\r\n   * @param y             判定を行うY座標\r\n   */ hitTest(hitArenaName, x, y) {\n        // 透明時は当たり判定無し。\n        if (this._opacity < 1) {\n            return false;\n        }\n        const count = this._modelSetting.getHitAreasCount();\n        for(let i = 0; i < count; i++){\n            if (this._modelSetting.getHitAreaName(i) == hitArenaName) {\n                const drawId = this._modelSetting.getHitAreaId(i);\n                return this.isHit(drawId, x, y);\n            }\n        }\n        return false;\n    }\n    /**\r\n   * モーションデータをグループ名から一括でロードする。\r\n   * モーションデータの名前は内部でModelSettingから取得する。\r\n   *\r\n   * @param group モーションデータのグループ名\r\n   */ preLoadMotionGroup(group) {\n        for(let i = 0; i < this._modelSetting.getMotionCount(group); i++){\n            const motionFileName = this._modelSetting.getMotionFileName(group, i);\n            // ex) idle_0\n            const name = \"\".concat(group, \"_\").concat(i);\n            if (this._debugMode) {\n                _lapppal__WEBPACK_IMPORTED_MODULE_12__.LAppPal.printMessage(\"[APP]load motion: \".concat(motionFileName, \" => [\").concat(name, \"]\"));\n            }\n            fetch(\"\".concat(this._modelHomeDir).concat(motionFileName)).then((response)=>{\n                if (response.ok) {\n                    return response.arrayBuffer();\n                } else if (response.status >= 400) {\n                    (0,_framework_utils_cubismdebug__WEBPACK_IMPORTED_MODULE_10__.CubismLogError)(\"Failed to load file \".concat(this._modelHomeDir).concat(motionFileName));\n                    return new ArrayBuffer(0);\n                }\n            }).then((arrayBuffer)=>{\n                const tmpMotion = this.loadMotion(arrayBuffer, arrayBuffer.byteLength, name, null, null, this._modelSetting, group, i);\n                if (tmpMotion != null) {\n                    tmpMotion.setEffectIds(this._eyeBlinkIds, this._lipSyncIds);\n                    if (this._motions.getValue(name) != null) {\n                        _framework_motion_acubismmotion__WEBPACK_IMPORTED_MODULE_6__.ACubismMotion.delete(this._motions.getValue(name));\n                    }\n                    this._motions.setValue(name, tmpMotion);\n                    this._motionCount++;\n                    if (this._motionCount >= this._allMotionCount) {\n                        this._state = 20;\n                        // 全てのモーションを停止する\n                        this._motionManager.stopAllMotions();\n                        this._updating = false;\n                        this._initialized = true;\n                        this.createRenderer();\n                        this.setupTextures();\n                        this.getRenderer().startUp(this._subdelegate.getGlManager().getGl());\n                    }\n                } else {\n                    // loadMotionできなかった場合はモーションの総数がずれるので1つ減らす\n                    this._allMotionCount--;\n                }\n            });\n        }\n    }\n    /**\r\n   * すべてのモーションデータを解放する。\r\n   */ releaseMotions() {\n        this._motions.clear();\n    }\n    /**\r\n   * 全ての表情データを解放する。\r\n   */ releaseExpressions() {\n        this._expressions.clear();\n    }\n    /**\r\n   * モデルを描画する処理。モデルを描画する空間のView-Projection行列を渡す。\r\n   */ doDraw() {\n        if (this._model == null) return;\n        // キャンバスサイズを渡す\n        const canvas = this._subdelegate.getCanvas();\n        const viewport = [\n            0,\n            0,\n            canvas.width,\n            canvas.height\n        ];\n        this.getRenderer().setRenderState(this._subdelegate.getFrameBuffer(), viewport);\n        this.getRenderer().drawModel();\n    }\n    /**\r\n   * モデルを描画する処理。モデルを描画する空間のView-Projection行列を渡す。\r\n   */ draw(matrix) {\n        if (this._model == null) {\n            return;\n        }\n        // 各読み込み終了後\n        if (this._state == 22) {\n            matrix.multiplyByMatrix(this._modelMatrix);\n            this.getRenderer().setMvpMatrix(matrix);\n            this.doDraw();\n        }\n    }\n    async hasMocConsistencyFromFile() {\n        (0,_framework_utils_cubismdebug__WEBPACK_IMPORTED_MODULE_10__.CSM_ASSERT)(this._modelSetting.getModelFileName().localeCompare(\"\"));\n        // CubismModel\n        if (this._modelSetting.getModelFileName() != \"\") {\n            const modelFileName = this._modelSetting.getModelFileName();\n            const response = await fetch(\"\".concat(this._modelHomeDir).concat(modelFileName));\n            const arrayBuffer = await response.arrayBuffer();\n            this._consistency = _framework_model_cubismmoc__WEBPACK_IMPORTED_MODULE_14__.CubismMoc.hasMocConsistency(arrayBuffer);\n            if (!this._consistency) {\n                (0,_framework_utils_cubismdebug__WEBPACK_IMPORTED_MODULE_10__.CubismLogInfo)(\"Inconsistent MOC3.\");\n            } else {\n                (0,_framework_utils_cubismdebug__WEBPACK_IMPORTED_MODULE_10__.CubismLogInfo)(\"Consistent MOC3.\");\n            }\n            return this._consistency;\n        } else {\n            _lapppal__WEBPACK_IMPORTED_MODULE_12__.LAppPal.printMessage(\"Model data does not exist.\");\n        }\n    }\n    setSubdelegate(subdelegate) {\n        this._subdelegate = subdelegate;\n    }\n    /**\r\n   * コンストラクタ\r\n   */ constructor(){\n        super();\n        this._modelSetting = null;\n        this._modelHomeDir = null;\n        this._userTimeSeconds = 0.0;\n        this._eyeBlinkIds = new _framework_type_csmvector__WEBPACK_IMPORTED_MODULE_9__.csmVector();\n        this._lipSyncIds = new _framework_type_csmvector__WEBPACK_IMPORTED_MODULE_9__.csmVector();\n        this._motions = new _framework_type_csmmap__WEBPACK_IMPORTED_MODULE_8__.csmMap();\n        this._expressions = new _framework_type_csmmap__WEBPACK_IMPORTED_MODULE_8__.csmMap();\n        this._hitArea = new _framework_type_csmvector__WEBPACK_IMPORTED_MODULE_9__.csmVector();\n        this._userArea = new _framework_type_csmvector__WEBPACK_IMPORTED_MODULE_9__.csmVector();\n        this._idParamAngleX = _framework_live2dcubismframework__WEBPACK_IMPORTED_MODULE_4__.CubismFramework.getIdManager().getId(_framework_cubismdefaultparameterid__WEBPACK_IMPORTED_MODULE_0__.CubismDefaultParameterId.ParamAngleX);\n        this._idParamAngleY = _framework_live2dcubismframework__WEBPACK_IMPORTED_MODULE_4__.CubismFramework.getIdManager().getId(_framework_cubismdefaultparameterid__WEBPACK_IMPORTED_MODULE_0__.CubismDefaultParameterId.ParamAngleY);\n        this._idParamAngleZ = _framework_live2dcubismframework__WEBPACK_IMPORTED_MODULE_4__.CubismFramework.getIdManager().getId(_framework_cubismdefaultparameterid__WEBPACK_IMPORTED_MODULE_0__.CubismDefaultParameterId.ParamAngleZ);\n        this._idParamEyeBallX = _framework_live2dcubismframework__WEBPACK_IMPORTED_MODULE_4__.CubismFramework.getIdManager().getId(_framework_cubismdefaultparameterid__WEBPACK_IMPORTED_MODULE_0__.CubismDefaultParameterId.ParamEyeBallX);\n        this._idParamEyeBallY = _framework_live2dcubismframework__WEBPACK_IMPORTED_MODULE_4__.CubismFramework.getIdManager().getId(_framework_cubismdefaultparameterid__WEBPACK_IMPORTED_MODULE_0__.CubismDefaultParameterId.ParamEyeBallY);\n        this._idParamBodyAngleX = _framework_live2dcubismframework__WEBPACK_IMPORTED_MODULE_4__.CubismFramework.getIdManager().getId(_framework_cubismdefaultparameterid__WEBPACK_IMPORTED_MODULE_0__.CubismDefaultParameterId.ParamBodyAngleX);\n        if (_lappdefine__WEBPACK_IMPORTED_MODULE_11__.MOCConsistencyValidationEnable) {\n            this._mocConsistency = true;\n        }\n        this._state = 0;\n        this._expressionCount = 0;\n        this._textureCount = 0;\n        this._motionCount = 0;\n        this._allMotionCount = 0;\n        this._wavFileHandler = new _lappwavfilehandler__WEBPACK_IMPORTED_MODULE_13__.LAppWavFileHandler();\n        this._consistency = false;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./lib/live2d/src/lappmodel.ts\n"));

/***/ })

});