'use client'

import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { getAvailableCharacters, CharacterInfo } from '../lib/characters';
import { useCharacterStore } from '../../lib/store/character';
import { useLive2D } from '@/hooks/useLive2D';

interface CharacterGalleryProps {
  isOpen: boolean;
  onClose: () => void;
}

export function CharacterGallery({ isOpen, onClose }: CharacterGalleryProps) {
  const { character, setCharacter } = useCharacterStore();
  const { setLive2dCharacter } = useLive2D();
  const characters = getAvailableCharacters();

  const handleCharacterSelect = (selectedCharacter: CharacterInfo) => {
    console.log('[CharacterGallery] Selecting character:', selectedCharacter.name);
    // Only update the character store, Live2D component will handle the loading
    setCharacter(selectedCharacter.resourceModel);
    onClose();

    // Restore focus to the search input after a short delay
    setTimeout(() => {
      const searchInput = document.getElementById('search-bar');
      if (searchInput) {
        searchInput.focus();
      }
    }, 100);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle>数字人画廊</DialogTitle>
          <DialogDescription>
            选择您喜欢的数字人模型
          </DialogDescription>
        </DialogHeader>
        
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4 max-h-96 overflow-y-auto p-4">
          {characters.map((char) => {
            const isSelected = char.resourceModel.resource_id === character?.resource_id;
            
            return (
              <div
                key={char.name}
                className={`flex flex-col items-center p-4 rounded-lg border-2 cursor-pointer transition-all hover:shadow-md ${
                  isSelected 
                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
                    : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                }`}
                onClick={() => handleCharacterSelect(char)}
              >
                <div className="relative">
                  <img
                    src={char.thumbnail}
                    alt={char.displayName}
                    className="w-20 h-20 rounded-full object-cover mb-3 border-2 border-transparent"
                    onError={(e) => {
                      // Fallback to a default image if thumbnail fails to load
                      (e.target as HTMLImageElement).src = '/favicon.ico';
                    }}
                  />
                  {isSelected && (
                    <div className="absolute -top-1 -right-1 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                      <svg
                        className="w-4 h-4 text-white"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M5 13l4 4L19 7"
                        />
                      </svg>
                    </div>
                  )}
                </div>
                
                <h3 className="text-sm font-medium text-center text-gray-900 dark:text-gray-100 mb-1">
                  {char.displayName}
                </h3>
                
                <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
                  Live2D 模型
                </p>
              </div>
            );
          })}
        </div>
        
        <div className="flex justify-between items-center pt-4 border-t border-gray-200 dark:border-gray-700">
          <p className="text-sm text-gray-500 dark:text-gray-400">
            共 {characters.length} 个可用模型
          </p>
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-800 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
          >
            关闭
          </button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
