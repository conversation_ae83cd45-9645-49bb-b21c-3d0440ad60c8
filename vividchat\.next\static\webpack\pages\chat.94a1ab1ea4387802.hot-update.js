"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/chat",{

/***/ "./src/components/CharacterGallery.tsx":
/*!*********************************************!*\
  !*** ./src/components/CharacterGallery.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CharacterGallery: function() { return /* binding */ CharacterGallery; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/dialog */ \"./src/components/ui/dialog.tsx\");\n/* harmony import */ var _lib_characters__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../lib/characters */ \"./src/lib/characters.ts\");\n/* harmony import */ var _lib_store_character__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../lib/store/character */ \"./lib/store/character.ts\");\n/* harmony import */ var _hooks_useLive2D__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useLive2D */ \"./src/hooks/useLive2D.ts\");\n/* __next_internal_client_entry_do_not_use__ CharacterGallery auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction CharacterGallery(param) {\n    let { isOpen, onClose } = param;\n    _s();\n    const { character, setCharacter } = (0,_lib_store_character__WEBPACK_IMPORTED_MODULE_4__.useCharacterStore)();\n    const { setLive2dCharacter } = (0,_hooks_useLive2D__WEBPACK_IMPORTED_MODULE_5__.useLive2D)();\n    const characters = (0,_lib_characters__WEBPACK_IMPORTED_MODULE_3__.getAvailableCharacters)();\n    const handleCharacterSelect = (selectedCharacter)=>{\n        console.log(\"[CharacterGallery] Selecting character:\", selectedCharacter.name);\n        // Check if it's the same character to avoid unnecessary loading (like livechat does)\n        if (character && character.name === selectedCharacter.resourceModel.name && character.resource_id === selectedCharacter.resourceModel.resource_id) {\n            console.log(\"[CharacterGallery] Same character selected, skipping\");\n            onClose();\n            return;\n        }\n        // Update both store and Live2D directly, like livechat does\n        console.log(\"[CharacterGallery] Setting new character:\", selectedCharacter.resourceModel);\n        setCharacter(selectedCharacter.resourceModel);\n        setLive2dCharacter(selectedCharacter.resourceModel);\n        onClose();\n        // Restore focus to the search input after a short delay\n        setTimeout(()=>{\n            const searchInput = document.getElementById(\"search-bar\");\n            if (searchInput) {\n                searchInput.focus();\n            }\n        }, 100);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.Dialog, {\n        open: isOpen,\n        onOpenChange: onClose,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogContent, {\n            className: \"max-w-4xl max-h-[80vh] overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogHeader, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogTitle, {\n                            children: \"数字人画廊\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\vividchat\\\\src\\\\components\\\\CharacterGallery.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogDescription, {\n                            children: \"选择您喜欢的数字人模型\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\vividchat\\\\src\\\\components\\\\CharacterGallery.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\vividchat\\\\src\\\\components\\\\CharacterGallery.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4 max-h-96 overflow-y-auto p-4\",\n                    children: characters.map((char)=>{\n                        const isSelected = char.resourceModel.resource_id === (character === null || character === void 0 ? void 0 : character.resource_id);\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center p-4 rounded-lg border-2 cursor-pointer transition-all hover:shadow-md \".concat(isSelected ? \"border-blue-500 bg-blue-50 dark:bg-blue-900/20\" : \"border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600\"),\n                            onClick: ()=>handleCharacterSelect(char),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: char.thumbnail,\n                                            alt: char.displayName,\n                                            className: \"w-20 h-20 rounded-full object-cover mb-3 border-2 border-transparent\",\n                                            onError: (e)=>{\n                                                // Fallback to a default image if thumbnail fails to load\n                                                e.target.src = \"/favicon.ico\";\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\vividchat\\\\src\\\\components\\\\CharacterGallery.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 19\n                                        }, this),\n                                        isSelected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute -top-1 -right-1 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4 text-white\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M5 13l4 4L19 7\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\vividchat\\\\src\\\\components\\\\CharacterGallery.tsx\",\n                                                    lineNumber: 91,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\vividchat\\\\src\\\\components\\\\CharacterGallery.tsx\",\n                                                lineNumber: 85,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\vividchat\\\\src\\\\components\\\\CharacterGallery.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\vividchat\\\\src\\\\components\\\\CharacterGallery.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium text-center text-gray-900 dark:text-gray-100 mb-1\",\n                                    children: char.displayName\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\vividchat\\\\src\\\\components\\\\CharacterGallery.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500 dark:text-gray-400 text-center\",\n                                    children: \"Live2D 模型\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\vividchat\\\\src\\\\components\\\\CharacterGallery.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, char.name, true, {\n                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\vividchat\\\\src\\\\components\\\\CharacterGallery.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 15\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\vividchat\\\\src\\\\components\\\\CharacterGallery.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center pt-4 border-t border-gray-200 dark:border-gray-700\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500 dark:text-gray-400\",\n                            children: [\n                                \"共 \",\n                                characters.length,\n                                \" 个可用模型\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\vividchat\\\\src\\\\components\\\\CharacterGallery.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-800 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors\",\n                            children: \"关闭\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\vividchat\\\\src\\\\components\\\\CharacterGallery.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\vividchat\\\\src\\\\components\\\\CharacterGallery.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\vividchat\\\\src\\\\components\\\\CharacterGallery.tsx\",\n            lineNumber: 51,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\vividchat\\\\src\\\\components\\\\CharacterGallery.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n_s(CharacterGallery, \"EAPQSoL0lQGx4asyLEi8Hb58ykw=\", false, function() {\n    return [\n        _lib_store_character__WEBPACK_IMPORTED_MODULE_4__.useCharacterStore,\n        _hooks_useLive2D__WEBPACK_IMPORTED_MODULE_5__.useLive2D\n    ];\n});\n_c = CharacterGallery;\nvar _c;\n$RefreshReg$(_c, \"CharacterGallery\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/CharacterGallery.tsx\n"));

/***/ })

});