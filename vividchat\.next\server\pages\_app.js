/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_app";
exports.ids = ["pages/_app"];
exports.modules = {

/***/ "./src/pages/_app.tsx":
/*!****************************!*\
  !*** ./src/pages/_app.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ pageWrapperTemplate),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/script */ \"./node_modules/next/script.js\");\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_script__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _context_UserContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/context/UserContext */ \"./src/context/UserContext.tsx\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/styles/globals.css */ \"./src/styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _sentry_nextjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @sentry/nextjs */ \"@sentry/nextjs\");\n/* harmony import */ var _sentry_nextjs__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_sentry_nextjs__WEBPACK_IMPORTED_MODULE_7__);\n\n\n\n\n\n\n\n\n\nfunction App({ Component, pageProps }) {\n    (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    // Get the layout function if it exists, otherwise use a default\n    const getLayout = Component.getLayout ?? ((page)=>page);\n    // Handle client-side routing for authentication\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_1___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"VividChat\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\vividchat\\\\src\\\\pages\\\\_app.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Standalone intelligent conversation frontend\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\vividchat\\\\src\\\\pages\\\\_app.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\vividchat\\\\src\\\\pages\\\\_app.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\vividchat\\\\src\\\\pages\\\\_app.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\vividchat\\\\src\\\\pages\\\\_app.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_script__WEBPACK_IMPORTED_MODULE_2___default()), {\n                src: \"/env-config.js\",\n                strategy: \"beforeInteractive\"\n            }, void 0, false, {\n                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\vividchat\\\\src\\\\pages\\\\_app.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_script__WEBPACK_IMPORTED_MODULE_2___default()), {\n                src: \"/sentio/core/live2dcubismcore.min.js\",\n                strategy: \"beforeInteractive\"\n            }, void 0, false, {\n                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\vividchat\\\\src\\\\pages\\\\_app.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_UserContext__WEBPACK_IMPORTED_MODULE_5__.UserProvider, {\n                children: getLayout(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                    ...pageProps\n                }, void 0, false, {\n                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\vividchat\\\\src\\\\pages\\\\_app.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 20\n                }, this))\n            }, void 0, false, {\n                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\vividchat\\\\src\\\\pages\\\\_app.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n\nvar serverComponentModule = /*#__PURE__*/Object.freeze({\n    __proto__: null,\n    default: App\n});\n\n/*\n * This file is a template for the code which will be substituted when our webpack loader handles non-API files in the\n * `pages/` directory.\n *\n * We use `__SENTRY_WRAPPING_TARGET_FILE__.cjs` as a placeholder for the path to the file being wrapped. Because it's not a real package,\n * this causes both TS and ESLint to complain, hence the pragma comments below.\n */\n\n\nconst userPageModule = serverComponentModule ;\n\nconst pageComponent = userPageModule ? userPageModule.default : undefined;\n\nconst origGetInitialProps = pageComponent ? pageComponent.getInitialProps : undefined;\nconst origGetStaticProps = userPageModule ? userPageModule.getStaticProps : undefined;\nconst origGetServerSideProps = userPageModule ? userPageModule.getServerSideProps : undefined;\n\n// Rollup will aggressively tree-shake what it perceives to be unused properties\n// on objects. Because the key that's used to index into this object (/_app)\n// is replaced during bundling, Rollup can't see that these properties are in fact\n// used. Using `Object.freeze` signals to Rollup that it should not tree-shake\n// this object.\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nconst getInitialPropsWrappers = Object.freeze({\n  '/_app': _sentry_nextjs__WEBPACK_IMPORTED_MODULE_7__.wrapAppGetInitialPropsWithSentry,\n  '/_document': _sentry_nextjs__WEBPACK_IMPORTED_MODULE_7__.wrapDocumentGetInitialPropsWithSentry,\n  '/_error': _sentry_nextjs__WEBPACK_IMPORTED_MODULE_7__.wrapErrorGetInitialPropsWithSentry,\n});\n\nconst getInitialPropsWrapper = getInitialPropsWrappers['/_app'] || _sentry_nextjs__WEBPACK_IMPORTED_MODULE_7__.wrapGetInitialPropsWithSentry;\n\nif (pageComponent && typeof origGetInitialProps === 'function') {\n  pageComponent.getInitialProps = getInitialPropsWrapper(origGetInitialProps) ;\n}\n\nconst getStaticProps =\n  typeof origGetStaticProps === 'function'\n    ? _sentry_nextjs__WEBPACK_IMPORTED_MODULE_7__.wrapGetStaticPropsWithSentry(origGetStaticProps, '/_app')\n    : undefined;\nconst getServerSideProps =\n  typeof origGetServerSideProps === 'function'\n    ? _sentry_nextjs__WEBPACK_IMPORTED_MODULE_7__.wrapGetServerSidePropsWithSentry(origGetServerSideProps, '/_app')\n    : undefined;\n\nconst pageWrapperTemplate = pageComponent ? _sentry_nextjs__WEBPACK_IMPORTED_MODULE_7__.wrapPageComponentWithSentry(pageComponent ) : pageComponent;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/_app.tsx\n");

/***/ }),

/***/ "./src/config/chatConfig.ts":
/*!**********************************!*\
  !*** ./src/config/chatConfig.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultChatConfig: () => (/* binding */ defaultChatConfig),\n/* harmony export */   getServerUrl: () => (/* binding */ getServerUrl),\n/* harmony export */   loadChatConfig: () => (/* binding */ loadChatConfig),\n/* harmony export */   validateConfig: () => (/* binding */ validateConfig)\n/* harmony export */ });\n/**\n * Default configuration values\n */ const defaultChatConfig = {\n    server: {\n        apiUrl: \"http://localhost:7272\",\n        useHttps: false,\n        apiVersion: \"v3\",\n        timeout: 30000\n    },\n    app: {\n        appName: \"VividChat\",\n        appDescription: \"Standalone intelligent conversation frontend\",\n        version: \"1.0.0\",\n        defaultMode: \"rag_agent\",\n        conversationHistoryLimit: 10\n    },\n    vectorSearch: {\n        enabled: true,\n        searchLimit: 10,\n        searchFilters: \"{}\",\n        indexMeasure: \"cosine_distance\",\n        includeMetadatas: false,\n        probes: undefined,\n        efSearch: undefined\n    },\n    hybridSearch: {\n        enabled: false,\n        fullTextWeight: undefined,\n        semanticWeight: undefined,\n        fullTextLimit: undefined,\n        rrfK: undefined\n    },\n    graphSearch: {\n        enabled: false,\n        kgSearchLevel: 0,\n        maxCommunityDescriptionLength: 200,\n        localSearchLimits: undefined\n    },\n    ragGeneration: {\n        temperature: 0.1,\n        topP: 1.0,\n        topK: 100,\n        maxTokensToSample: 1024\n    }\n};\n/**\n * Load configuration from public/config.json\n */ async function loadChatConfig() {\n    try {\n        const response = await fetch(\"/config.json\");\n        if (!response.ok) {\n            console.warn(\"Failed to load config.json, using default configuration\");\n            return defaultChatConfig;\n        }\n        const config = await response.json();\n        // Merge with defaults to ensure all required fields are present\n        return {\n            server: {\n                ...defaultChatConfig.server,\n                ...config.server\n            },\n            app: {\n                ...defaultChatConfig.app,\n                ...config.app\n            },\n            vectorSearch: {\n                ...defaultChatConfig.vectorSearch,\n                ...config.vectorSearch\n            },\n            hybridSearch: {\n                ...defaultChatConfig.hybridSearch,\n                ...config.hybridSearch\n            },\n            graphSearch: {\n                ...defaultChatConfig.graphSearch,\n                ...config.graphSearch\n            },\n            ragGeneration: {\n                ...defaultChatConfig.ragGeneration,\n                ...config.ragGeneration\n            }\n        };\n    } catch (error) {\n        console.error(\"Error loading configuration:\", error);\n        return defaultChatConfig;\n    }\n}\n/**\n * Get server URL from configuration\n */ function getServerUrl(config) {\n    const { apiUrl, useHttps, apiVersion } = config.server;\n    const protocol = useHttps ? \"https\" : \"http\";\n    const baseUrl = apiUrl.startsWith(\"http\") ? apiUrl : `${protocol}://${apiUrl}`;\n    return apiVersion ? `${baseUrl}/${apiVersion}` : baseUrl;\n}\n/**\n * Validate configuration\n */ function validateConfig(config) {\n    try {\n        // Check required fields\n        if (!config.server?.apiUrl) {\n            console.error(\"Configuration validation failed: server.apiUrl is required\");\n            return false;\n        }\n        if (!config.app?.appName) {\n            console.error(\"Configuration validation failed: app.appName is required\");\n            return false;\n        }\n        // Validate mode\n        if (![\n            \"rag\",\n            \"rag_agent\"\n        ].includes(config.app.defaultMode)) {\n            console.error('Configuration validation failed: app.defaultMode must be \"rag\" or \"rag_agent\"');\n            return false;\n        }\n        // Validate search limits\n        if (config.vectorSearch.searchLimit <= 0) {\n            console.error(\"Configuration validation failed: vectorSearch.searchLimit must be positive\");\n            return false;\n        }\n        return true;\n    } catch (error) {\n        console.error(\"Configuration validation error:\", error);\n        return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29uZmlnL2NoYXRDb25maWcudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUVBOztDQUVDLEdBQ00sTUFBTUEsb0JBQWdDO0lBQzNDQyxRQUFRO1FBQ05DLFFBQVE7UUFDUkMsVUFBVTtRQUNWQyxZQUFZO1FBQ1pDLFNBQVM7SUFDWDtJQUNBQyxLQUFLO1FBQ0hDLFNBQVM7UUFDVEMsZ0JBQWdCO1FBQ2hCQyxTQUFTO1FBQ1RDLGFBQWE7UUFDYkMsMEJBQTBCO0lBQzVCO0lBQ0FDLGNBQWM7UUFDWkMsU0FBUztRQUNUQyxhQUFhO1FBQ2JDLGVBQWU7UUFDZkMsY0FBYztRQUNkQyxrQkFBa0I7UUFDbEJDLFFBQVFDO1FBQ1JDLFVBQVVEO0lBQ1o7SUFDQUUsY0FBYztRQUNaUixTQUFTO1FBQ1RTLGdCQUFnQkg7UUFDaEJJLGdCQUFnQko7UUFDaEJLLGVBQWVMO1FBQ2ZNLE1BQU1OO0lBQ1I7SUFDQU8sYUFBYTtRQUNYYixTQUFTO1FBQ1RjLGVBQWU7UUFDZkMsK0JBQStCO1FBQy9CQyxtQkFBbUJWO0lBQ3JCO0lBQ0FXLGVBQWU7UUFDYkMsYUFBYTtRQUNiQyxNQUFNO1FBQ05DLE1BQU07UUFDTkMsbUJBQW1CO0lBQ3JCO0FBQ0YsRUFBRTtBQUVGOztDQUVDLEdBQ00sZUFBZUM7SUFDcEIsSUFBSTtRQUNGLE1BQU1DLFdBQVcsTUFBTUMsTUFBTTtRQUM3QixJQUFJLENBQUNELFNBQVNFLEVBQUUsRUFBRTtZQUNoQkMsUUFBUUMsSUFBSSxDQUFDO1lBQ2IsT0FBT3hDO1FBQ1Q7UUFFQSxNQUFNeUMsU0FBUyxNQUFNTCxTQUFTTSxJQUFJO1FBRWxDLGdFQUFnRTtRQUNoRSxPQUFPO1lBQ0x6QyxRQUFRO2dCQUNOLEdBQUdELGtCQUFrQkMsTUFBTTtnQkFDM0IsR0FBR3dDLE9BQU94QyxNQUFNO1lBQ2xCO1lBQ0FLLEtBQUs7Z0JBQ0gsR0FBR04sa0JBQWtCTSxHQUFHO2dCQUN4QixHQUFHbUMsT0FBT25DLEdBQUc7WUFDZjtZQUNBTSxjQUFjO2dCQUNaLEdBQUdaLGtCQUFrQlksWUFBWTtnQkFDakMsR0FBRzZCLE9BQU83QixZQUFZO1lBQ3hCO1lBQ0FTLGNBQWM7Z0JBQ1osR0FBR3JCLGtCQUFrQnFCLFlBQVk7Z0JBQ2pDLEdBQUdvQixPQUFPcEIsWUFBWTtZQUN4QjtZQUNBSyxhQUFhO2dCQUNYLEdBQUcxQixrQkFBa0IwQixXQUFXO2dCQUNoQyxHQUFHZSxPQUFPZixXQUFXO1lBQ3ZCO1lBQ0FJLGVBQWU7Z0JBQ2IsR0FBRzlCLGtCQUFrQjhCLGFBQWE7Z0JBQ2xDLEdBQUdXLE9BQU9YLGFBQWE7WUFDekI7UUFDRjtJQUNGLEVBQUUsT0FBT2EsT0FBTztRQUNkSixRQUFRSSxLQUFLLENBQUMsZ0NBQWdDQTtRQUM5QyxPQUFPM0M7SUFDVDtBQUNGO0FBRUE7O0NBRUMsR0FDTSxTQUFTNEMsYUFBYUgsTUFBa0I7SUFDN0MsTUFBTSxFQUFFdkMsTUFBTSxFQUFFQyxRQUFRLEVBQUVDLFVBQVUsRUFBRSxHQUFHcUMsT0FBT3hDLE1BQU07SUFDdEQsTUFBTTRDLFdBQVcxQyxXQUFXLFVBQVU7SUFDdEMsTUFBTTJDLFVBQVU1QyxPQUFPNkMsVUFBVSxDQUFDLFVBQVU3QyxTQUFTLENBQUMsRUFBRTJDLFNBQVMsR0FBRyxFQUFFM0MsT0FBTyxDQUFDO0lBQzlFLE9BQU9FLGFBQWEsQ0FBQyxFQUFFMEMsUUFBUSxDQUFDLEVBQUUxQyxXQUFXLENBQUMsR0FBRzBDO0FBQ25EO0FBRUE7O0NBRUMsR0FDTSxTQUFTRSxlQUFlUCxNQUFrQjtJQUMvQyxJQUFJO1FBQ0Ysd0JBQXdCO1FBQ3hCLElBQUksQ0FBQ0EsT0FBT3hDLE1BQU0sRUFBRUMsUUFBUTtZQUMxQnFDLFFBQVFJLEtBQUssQ0FBQztZQUNkLE9BQU87UUFDVDtRQUVBLElBQUksQ0FBQ0YsT0FBT25DLEdBQUcsRUFBRUMsU0FBUztZQUN4QmdDLFFBQVFJLEtBQUssQ0FBQztZQUNkLE9BQU87UUFDVDtRQUVBLGdCQUFnQjtRQUNoQixJQUFJLENBQUM7WUFBQztZQUFPO1NBQVksQ0FBQ00sUUFBUSxDQUFDUixPQUFPbkMsR0FBRyxDQUFDSSxXQUFXLEdBQUc7WUFDMUQ2QixRQUFRSSxLQUFLLENBQUM7WUFDZCxPQUFPO1FBQ1Q7UUFFQSx5QkFBeUI7UUFDekIsSUFBSUYsT0FBTzdCLFlBQVksQ0FBQ0UsV0FBVyxJQUFJLEdBQUc7WUFDeEN5QixRQUFRSSxLQUFLLENBQUM7WUFDZCxPQUFPO1FBQ1Q7UUFFQSxPQUFPO0lBQ1QsRUFBRSxPQUFPQSxPQUFPO1FBQ2RKLFFBQVFJLEtBQUssQ0FBQyxtQ0FBbUNBO1FBQ2pELE9BQU87SUFDVDtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdml2aWRjaGF0Ly4vc3JjL2NvbmZpZy9jaGF0Q29uZmlnLnRzPzY5MmEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQ2hhdENvbmZpZyB9IGZyb20gJy4uL3R5cGVzJztcblxuLyoqXG4gKiBEZWZhdWx0IGNvbmZpZ3VyYXRpb24gdmFsdWVzXG4gKi9cbmV4cG9ydCBjb25zdCBkZWZhdWx0Q2hhdENvbmZpZzogQ2hhdENvbmZpZyA9IHtcbiAgc2VydmVyOiB7XG4gICAgYXBpVXJsOiBcImh0dHA6Ly9sb2NhbGhvc3Q6NzI3MlwiLFxuICAgIHVzZUh0dHBzOiBmYWxzZSxcbiAgICBhcGlWZXJzaW9uOiBcInYzXCIsXG4gICAgdGltZW91dDogMzAwMDAsXG4gIH0sXG4gIGFwcDoge1xuICAgIGFwcE5hbWU6IFwiVml2aWRDaGF0XCIsXG4gICAgYXBwRGVzY3JpcHRpb246IFwiU3RhbmRhbG9uZSBpbnRlbGxpZ2VudCBjb252ZXJzYXRpb24gZnJvbnRlbmRcIixcbiAgICB2ZXJzaW9uOiBcIjEuMC4wXCIsXG4gICAgZGVmYXVsdE1vZGU6IFwicmFnX2FnZW50XCIsIC8vIEZpeGVkIHRvIEFnZW50IE1vZGVcbiAgICBjb252ZXJzYXRpb25IaXN0b3J5TGltaXQ6IDEwLFxuICB9LFxuICB2ZWN0b3JTZWFyY2g6IHtcbiAgICBlbmFibGVkOiB0cnVlLFxuICAgIHNlYXJjaExpbWl0OiAxMCxcbiAgICBzZWFyY2hGaWx0ZXJzOiBcInt9XCIsXG4gICAgaW5kZXhNZWFzdXJlOiBcImNvc2luZV9kaXN0YW5jZVwiLFxuICAgIGluY2x1ZGVNZXRhZGF0YXM6IGZhbHNlLFxuICAgIHByb2JlczogdW5kZWZpbmVkLFxuICAgIGVmU2VhcmNoOiB1bmRlZmluZWQsXG4gIH0sXG4gIGh5YnJpZFNlYXJjaDoge1xuICAgIGVuYWJsZWQ6IGZhbHNlLFxuICAgIGZ1bGxUZXh0V2VpZ2h0OiB1bmRlZmluZWQsXG4gICAgc2VtYW50aWNXZWlnaHQ6IHVuZGVmaW5lZCxcbiAgICBmdWxsVGV4dExpbWl0OiB1bmRlZmluZWQsXG4gICAgcnJmSzogdW5kZWZpbmVkLFxuICB9LFxuICBncmFwaFNlYXJjaDoge1xuICAgIGVuYWJsZWQ6IGZhbHNlLFxuICAgIGtnU2VhcmNoTGV2ZWw6IDAsXG4gICAgbWF4Q29tbXVuaXR5RGVzY3JpcHRpb25MZW5ndGg6IDIwMCxcbiAgICBsb2NhbFNlYXJjaExpbWl0czogdW5kZWZpbmVkLFxuICB9LFxuICByYWdHZW5lcmF0aW9uOiB7XG4gICAgdGVtcGVyYXR1cmU6IDAuMSxcbiAgICB0b3BQOiAxLjAsXG4gICAgdG9wSzogMTAwLFxuICAgIG1heFRva2Vuc1RvU2FtcGxlOiAxMDI0LFxuICB9LFxufTtcblxuLyoqXG4gKiBMb2FkIGNvbmZpZ3VyYXRpb24gZnJvbSBwdWJsaWMvY29uZmlnLmpzb25cbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGxvYWRDaGF0Q29uZmlnKCk6IFByb21pc2U8Q2hhdENvbmZpZz4ge1xuICB0cnkge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9jb25maWcuanNvbicpO1xuICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgIGNvbnNvbGUud2FybignRmFpbGVkIHRvIGxvYWQgY29uZmlnLmpzb24sIHVzaW5nIGRlZmF1bHQgY29uZmlndXJhdGlvbicpO1xuICAgICAgcmV0dXJuIGRlZmF1bHRDaGF0Q29uZmlnO1xuICAgIH1cbiAgICBcbiAgICBjb25zdCBjb25maWcgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgXG4gICAgLy8gTWVyZ2Ugd2l0aCBkZWZhdWx0cyB0byBlbnN1cmUgYWxsIHJlcXVpcmVkIGZpZWxkcyBhcmUgcHJlc2VudFxuICAgIHJldHVybiB7XG4gICAgICBzZXJ2ZXI6IHtcbiAgICAgICAgLi4uZGVmYXVsdENoYXRDb25maWcuc2VydmVyLFxuICAgICAgICAuLi5jb25maWcuc2VydmVyLFxuICAgICAgfSxcbiAgICAgIGFwcDoge1xuICAgICAgICAuLi5kZWZhdWx0Q2hhdENvbmZpZy5hcHAsXG4gICAgICAgIC4uLmNvbmZpZy5hcHAsXG4gICAgICB9LFxuICAgICAgdmVjdG9yU2VhcmNoOiB7XG4gICAgICAgIC4uLmRlZmF1bHRDaGF0Q29uZmlnLnZlY3RvclNlYXJjaCxcbiAgICAgICAgLi4uY29uZmlnLnZlY3RvclNlYXJjaCxcbiAgICAgIH0sXG4gICAgICBoeWJyaWRTZWFyY2g6IHtcbiAgICAgICAgLi4uZGVmYXVsdENoYXRDb25maWcuaHlicmlkU2VhcmNoLFxuICAgICAgICAuLi5jb25maWcuaHlicmlkU2VhcmNoLFxuICAgICAgfSxcbiAgICAgIGdyYXBoU2VhcmNoOiB7XG4gICAgICAgIC4uLmRlZmF1bHRDaGF0Q29uZmlnLmdyYXBoU2VhcmNoLFxuICAgICAgICAuLi5jb25maWcuZ3JhcGhTZWFyY2gsXG4gICAgICB9LFxuICAgICAgcmFnR2VuZXJhdGlvbjoge1xuICAgICAgICAuLi5kZWZhdWx0Q2hhdENvbmZpZy5yYWdHZW5lcmF0aW9uLFxuICAgICAgICAuLi5jb25maWcucmFnR2VuZXJhdGlvbixcbiAgICAgIH0sXG4gICAgfTtcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBsb2FkaW5nIGNvbmZpZ3VyYXRpb246JywgZXJyb3IpO1xuICAgIHJldHVybiBkZWZhdWx0Q2hhdENvbmZpZztcbiAgfVxufVxuXG4vKipcbiAqIEdldCBzZXJ2ZXIgVVJMIGZyb20gY29uZmlndXJhdGlvblxuICovXG5leHBvcnQgZnVuY3Rpb24gZ2V0U2VydmVyVXJsKGNvbmZpZzogQ2hhdENvbmZpZyk6IHN0cmluZyB7XG4gIGNvbnN0IHsgYXBpVXJsLCB1c2VIdHRwcywgYXBpVmVyc2lvbiB9ID0gY29uZmlnLnNlcnZlcjtcbiAgY29uc3QgcHJvdG9jb2wgPSB1c2VIdHRwcyA/ICdodHRwcycgOiAnaHR0cCc7XG4gIGNvbnN0IGJhc2VVcmwgPSBhcGlVcmwuc3RhcnRzV2l0aCgnaHR0cCcpID8gYXBpVXJsIDogYCR7cHJvdG9jb2x9Oi8vJHthcGlVcmx9YDtcbiAgcmV0dXJuIGFwaVZlcnNpb24gPyBgJHtiYXNlVXJsfS8ke2FwaVZlcnNpb259YCA6IGJhc2VVcmw7XG59XG5cbi8qKlxuICogVmFsaWRhdGUgY29uZmlndXJhdGlvblxuICovXG5leHBvcnQgZnVuY3Rpb24gdmFsaWRhdGVDb25maWcoY29uZmlnOiBDaGF0Q29uZmlnKTogYm9vbGVhbiB7XG4gIHRyeSB7XG4gICAgLy8gQ2hlY2sgcmVxdWlyZWQgZmllbGRzXG4gICAgaWYgKCFjb25maWcuc2VydmVyPy5hcGlVcmwpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0NvbmZpZ3VyYXRpb24gdmFsaWRhdGlvbiBmYWlsZWQ6IHNlcnZlci5hcGlVcmwgaXMgcmVxdWlyZWQnKTtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gICAgXG4gICAgaWYgKCFjb25maWcuYXBwPy5hcHBOYW1lKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdDb25maWd1cmF0aW9uIHZhbGlkYXRpb24gZmFpbGVkOiBhcHAuYXBwTmFtZSBpcyByZXF1aXJlZCcpO1xuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgICBcbiAgICAvLyBWYWxpZGF0ZSBtb2RlXG4gICAgaWYgKCFbJ3JhZycsICdyYWdfYWdlbnQnXS5pbmNsdWRlcyhjb25maWcuYXBwLmRlZmF1bHRNb2RlKSkge1xuICAgICAgY29uc29sZS5lcnJvcignQ29uZmlndXJhdGlvbiB2YWxpZGF0aW9uIGZhaWxlZDogYXBwLmRlZmF1bHRNb2RlIG11c3QgYmUgXCJyYWdcIiBvciBcInJhZ19hZ2VudFwiJyk7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICAgIFxuICAgIC8vIFZhbGlkYXRlIHNlYXJjaCBsaW1pdHNcbiAgICBpZiAoY29uZmlnLnZlY3RvclNlYXJjaC5zZWFyY2hMaW1pdCA8PSAwKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdDb25maWd1cmF0aW9uIHZhbGlkYXRpb24gZmFpbGVkOiB2ZWN0b3JTZWFyY2guc2VhcmNoTGltaXQgbXVzdCBiZSBwb3NpdGl2ZScpO1xuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgICBcbiAgICByZXR1cm4gdHJ1ZTtcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdDb25maWd1cmF0aW9uIHZhbGlkYXRpb24gZXJyb3I6JywgZXJyb3IpO1xuICAgIHJldHVybiBmYWxzZTtcbiAgfVxufVxuIl0sIm5hbWVzIjpbImRlZmF1bHRDaGF0Q29uZmlnIiwic2VydmVyIiwiYXBpVXJsIiwidXNlSHR0cHMiLCJhcGlWZXJzaW9uIiwidGltZW91dCIsImFwcCIsImFwcE5hbWUiLCJhcHBEZXNjcmlwdGlvbiIsInZlcnNpb24iLCJkZWZhdWx0TW9kZSIsImNvbnZlcnNhdGlvbkhpc3RvcnlMaW1pdCIsInZlY3RvclNlYXJjaCIsImVuYWJsZWQiLCJzZWFyY2hMaW1pdCIsInNlYXJjaEZpbHRlcnMiLCJpbmRleE1lYXN1cmUiLCJpbmNsdWRlTWV0YWRhdGFzIiwicHJvYmVzIiwidW5kZWZpbmVkIiwiZWZTZWFyY2giLCJoeWJyaWRTZWFyY2giLCJmdWxsVGV4dFdlaWdodCIsInNlbWFudGljV2VpZ2h0IiwiZnVsbFRleHRMaW1pdCIsInJyZksiLCJncmFwaFNlYXJjaCIsImtnU2VhcmNoTGV2ZWwiLCJtYXhDb21tdW5pdHlEZXNjcmlwdGlvbkxlbmd0aCIsImxvY2FsU2VhcmNoTGltaXRzIiwicmFnR2VuZXJhdGlvbiIsInRlbXBlcmF0dXJlIiwidG9wUCIsInRvcEsiLCJtYXhUb2tlbnNUb1NhbXBsZSIsImxvYWRDaGF0Q29uZmlnIiwicmVzcG9uc2UiLCJmZXRjaCIsIm9rIiwiY29uc29sZSIsIndhcm4iLCJjb25maWciLCJqc29uIiwiZXJyb3IiLCJnZXRTZXJ2ZXJVcmwiLCJwcm90b2NvbCIsImJhc2VVcmwiLCJzdGFydHNXaXRoIiwidmFsaWRhdGVDb25maWciLCJpbmNsdWRlcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/config/chatConfig.ts\n");

/***/ }),

/***/ "./src/context/UserContext.tsx":
/*!*************************************!*\
  !*** ./src/context/UserContext.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserProvider: () => (/* binding */ UserProvider),\n/* harmony export */   useUserContext: () => (/* binding */ useUserContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var r2r_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! r2r-js */ \"r2r-js\");\n/* harmony import */ var r2r_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(r2r_js__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _config_chatConfig__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/config/chatConfig */ \"./src/config/chatConfig.ts\");\n\n\n\n\n\nfunction isAuthState(obj) {\n    const validRoles = [\n        \"admin\",\n        \"user\",\n        null\n    ];\n    return typeof obj === \"object\" && obj !== null && typeof obj.isAuthenticated === \"boolean\" && (typeof obj.email === \"string\" || obj.email === null) && (validRoles.includes(obj.userRole) || obj.userRole === null);\n}\nconst UserContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_3__.createContext)({\n    pipeline: null,\n    setPipeline: ()=>{},\n    selectedModel: \"null\",\n    setSelectedModel: ()=>{},\n    isAuthenticated: false,\n    login: async ()=>({\n            success: false,\n            userRole: \"user\"\n        }),\n    loginWithToken: async ()=>({\n            success: false,\n            userRole: \"user\"\n        }),\n    logout: async ()=>{},\n    unsetCredentials: async ()=>{},\n    register: async ()=>{},\n    authState: {\n        isAuthenticated: false,\n        email: null,\n        userRole: null,\n        userId: null\n    },\n    getClient: ()=>null,\n    client: null,\n    viewMode: \"user\",\n    setViewMode: ()=>{},\n    isSuperUser: ()=>false\n});\nconst useUserContext = ()=>(0,react__WEBPACK_IMPORTED_MODULE_3__.useContext)(UserContext);\nconst UserProvider = ({ children })=>{\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const [isReady, setIsReady] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [client, setClient] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"user\");\n    const [pipeline, setPipeline] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(()=>{\n        if (false) {}\n        return null;\n    });\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(()=>{\n        if (false) {}\n        return \"null\";\n    });\n    const [authState, setAuthState] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(()=>{\n        if (false) {}\n        return {\n            isAuthenticated: false,\n            email: null,\n            userRole: null,\n            userId: null\n        };\n    });\n    // Initialize with configuration\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        const initializeApp = async ()=>{\n            try {\n                const config = await (0,_config_chatConfig__WEBPACK_IMPORTED_MODULE_4__.loadChatConfig)();\n                // Set default pipeline from config if not already set\n                if (!pipeline && config.server.apiUrl) {\n                    const defaultPipeline = {\n                        deploymentUrl: config.server.apiUrl\n                    };\n                    setPipeline(defaultPipeline);\n                    localStorage.setItem(\"vividchat_pipeline\", JSON.stringify(defaultPipeline));\n                }\n                // Try to restore session if tokens exist\n                const accessToken = localStorage.getItem(\"vividchat_accessToken\");\n                const refreshToken = localStorage.getItem(\"vividchat_refreshToken\");\n                if (accessToken && refreshToken && pipeline) {\n                    const newClient = new r2r_js__WEBPACK_IMPORTED_MODULE_2__.r2rClient(pipeline.deploymentUrl);\n                    newClient.setTokens(accessToken, refreshToken);\n                    try {\n                        // Verify token is still valid\n                        const userInfo = await newClient.users.me();\n                        if (userInfo.results) {\n                            setClient(newClient);\n                            // Check user role\n                            let userRole = \"user\";\n                            try {\n                                await newClient.system.settings();\n                                userRole = \"admin\";\n                            } catch (error) {\n                            // User doesn't have admin access\n                            }\n                            setAuthState({\n                                isAuthenticated: true,\n                                email: userInfo.results.email || \"\",\n                                userRole: userRole,\n                                userId: userInfo.results.id\n                            });\n                        }\n                    } catch (error) {\n                        // Token is invalid, clear it\n                        localStorage.removeItem(\"vividchat_accessToken\");\n                        localStorage.removeItem(\"vividchat_refreshToken\");\n                    }\n                }\n            } catch (error) {\n                console.error(\"Failed to initialize app:\", error);\n            } finally{\n                setIsReady(true);\n            }\n        };\n        initializeApp();\n    }, []);\n    const isSuperUser = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(()=>{\n        return authState.userRole === \"admin\" && viewMode === \"admin\";\n    }, [\n        authState.userRole,\n        viewMode\n    ]);\n    const login = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(async (email, password, instanceUrl)=>{\n        const newClient = new r2r_js__WEBPACK_IMPORTED_MODULE_2__.r2rClient(instanceUrl);\n        try {\n            const tokens = await newClient.users.login({\n                email: email,\n                password: password\n            });\n            localStorage.setItem(\"vividchat_accessToken\", tokens.results.accessToken.token);\n            localStorage.setItem(\"vividchat_refreshToken\", tokens.results.refreshToken.token);\n            newClient.setTokens(tokens.results.accessToken.token, tokens.results.refreshToken.token);\n            setClient(newClient);\n            // Get user info\n            const userInfo = await newClient.users.me();\n            if (!userInfo.results) {\n                throw new Error(\"Failed to get user information\");\n            }\n            // Check user role\n            let userRole = \"user\";\n            try {\n                await newClient.system.settings();\n                userRole = \"admin\";\n            } catch (error) {\n            // User doesn't have admin access\n            }\n            const newAuthState = {\n                isAuthenticated: true,\n                email,\n                userRole,\n                userId: userInfo.results.id\n            };\n            setAuthState(newAuthState);\n            localStorage.setItem(\"vividchat_authState\", JSON.stringify(newAuthState));\n            const newPipeline = {\n                deploymentUrl: instanceUrl\n            };\n            setPipeline(newPipeline);\n            localStorage.setItem(\"vividchat_pipeline\", JSON.stringify(newPipeline));\n            return {\n                success: true,\n                userRole\n            };\n        } catch (error) {\n            console.error(\"Login failed:\", error);\n            throw error;\n        }\n    }, []);\n    const loginWithToken = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(async (token, instanceUrl)=>{\n        const newClient = new r2r_js__WEBPACK_IMPORTED_MODULE_2__.r2rClient(instanceUrl);\n        try {\n            const result = await newClient.users.loginWithToken({\n                accessToken: token\n            });\n            const userInfo = await newClient.users.me();\n            localStorage.setItem(\"vividchat_accessToken\", result.accessToken.token);\n            newClient.setTokens(result.accessToken.token, \"\");\n            setClient(newClient);\n            let userRole = \"user\";\n            try {\n                await newClient.system.settings();\n                userRole = \"admin\";\n            } catch (error) {\n            // User doesn't have admin access\n            }\n            const newAuthState = {\n                isAuthenticated: true,\n                email: userInfo.results.email || \"\",\n                userRole,\n                userId: userInfo.results.id\n            };\n            setAuthState(newAuthState);\n            localStorage.setItem(\"vividchat_authState\", JSON.stringify(newAuthState));\n            const newPipeline = {\n                deploymentUrl: instanceUrl\n            };\n            setPipeline(newPipeline);\n            localStorage.setItem(\"vividchat_pipeline\", JSON.stringify(newPipeline));\n            return {\n                success: true,\n                userRole\n            };\n        } catch (error) {\n            console.error(\"Login with token failed:\", error);\n            throw error;\n        }\n    }, []);\n    const logout = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(async ()=>{\n        if (client && authState.isAuthenticated) {\n            try {\n                await client.users.logout();\n            } catch (error) {\n                console.error(`Logout failed:`, error);\n            }\n        }\n        setAuthState({\n            isAuthenticated: false,\n            email: null,\n            userRole: null,\n            userId: null\n        });\n        localStorage.removeItem(\"vividchat_pipeline\");\n        localStorage.removeItem(\"vividchat_authState\");\n        localStorage.removeItem(\"vividchat_accessToken\");\n        localStorage.removeItem(\"vividchat_refreshToken\");\n        setClient(null);\n    }, [\n        client,\n        authState.isAuthenticated\n    ]);\n    const unsetCredentials = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(async ()=>{\n        setAuthState({\n            isAuthenticated: false,\n            email: null,\n            userRole: null,\n            userId: null\n        });\n        localStorage.removeItem(\"vividchat_pipeline\");\n        localStorage.removeItem(\"vividchat_authState\");\n        localStorage.removeItem(\"vividchat_accessToken\");\n        localStorage.removeItem(\"vividchat_refreshToken\");\n        setClient(null);\n    }, []);\n    const register = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(async (email, password, instanceUrl)=>{\n        const newClient = new r2r_js__WEBPACK_IMPORTED_MODULE_2__.r2rClient(instanceUrl);\n        if (newClient) {\n            try {\n                await newClient.users.create({\n                    email: email,\n                    password: password\n                });\n            } catch (error) {\n                console.error(\"Failed to create user:\", error);\n                throw error;\n            }\n        }\n    }, []);\n    const getClient = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(()=>{\n        return client;\n    }, [\n        client\n    ]);\n    const contextValue = react__WEBPACK_IMPORTED_MODULE_3___default().useMemo(()=>({\n            pipeline,\n            setPipeline,\n            selectedModel,\n            setSelectedModel,\n            isAuthenticated: authState.isAuthenticated,\n            authState,\n            login,\n            loginWithToken,\n            logout,\n            unsetCredentials,\n            register,\n            getClient,\n            client,\n            viewMode,\n            setViewMode,\n            isSuperUser\n        }), [\n        pipeline,\n        selectedModel,\n        authState,\n        client,\n        viewMode,\n        isSuperUser,\n        login,\n        loginWithToken,\n        logout,\n        unsetCredentials,\n        register,\n        getClient\n    ]);\n    if (!isReady) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UserContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\vividchat\\\\src\\\\context\\\\UserContext.tsx\",\n        lineNumber: 370,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/context/UserContext.tsx\n");

/***/ }),

/***/ "./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ (() => {



/***/ }),

/***/ "@sentry/nextjs":
/*!*********************************!*\
  !*** external "@sentry/nextjs" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@sentry/nextjs");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/head");

/***/ }),

/***/ "r2r-js":
/*!*************************!*\
  !*** external "r2r-js" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("r2r-js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("./src/pages/_app.tsx")));
module.exports = __webpack_exports__;

})();