"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/chat",{

/***/ "./src/pages/chat.tsx":
/*!****************************!*\
  !*** ./src/pages/chat.tsx ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ChatDemo_result__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ChatDemo/result */ \"./src/components/ChatDemo/result.tsx\");\n/* harmony import */ var _components_ChatDemo_search__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ChatDemo/search */ \"./src/components/ChatDemo/search.tsx\");\n/* harmony import */ var _components_ChatDemo_SwitchManager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ChatDemo/SwitchManager */ \"./src/components/ChatDemo/SwitchManager.tsx\");\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/Layout */ \"./src/components/Layout/index.tsx\");\n/* harmony import */ var _components_Sidebar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/Sidebar */ \"./src/components/Sidebar.tsx\");\n/* harmony import */ var _components_Live2D__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/Live2D */ \"./src/components/Live2D.tsx\");\n/* harmony import */ var _context_UserContext__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/context/UserContext */ \"./src/context/UserContext.tsx\");\n/* harmony import */ var _config_chatConfig__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/config/chatConfig */ \"./src/config/chatConfig.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/utils */ \"./src/lib/utils.ts\");\n/* harmony import */ var _hooks_useLive2D__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/hooks/useLive2D */ \"./src/hooks/useLive2D.ts\");\n/* harmony import */ var _lib_store_character__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../../lib/store/character */ \"./lib/store/character.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ChatPage = ()=>{\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const { isAuthenticated, getClient, selectedModel, pipeline } = (0,_context_UserContext__WEBPACK_IMPORTED_MODULE_9__.useUserContext)();\n    const { setLive2dCharacter } = (0,_hooks_useLive2D__WEBPACK_IMPORTED_MODULE_12__.useLive2D)();\n    const { character } = (0,_lib_store_character__WEBPACK_IMPORTED_MODULE_13__.useCharacterStore)();\n    const [query, setQuery] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [hasAttemptedFetch, setHasAttemptedFetch] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [selectedCollectionIds, setSelectedCollectionIds] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [selectedConversationId, setSelectedConversationId] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [mode] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"rag_agent\"); // Fixed to Agent Mode\n    const [sidebarIsOpen, setSidebarIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [uploadedDocuments, setUploadedDocuments] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [collections, setCollections] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [userId, setUserId] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    // Configuration state\n    const [searchLimit, setSearchLimit] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(10);\n    const [searchFilters, setSearchFilters] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"{}\");\n    const [indexMeasure, setIndexMeasure] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"cosine_distance\");\n    const [includeMetadatas, setIncludeMetadatas] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [probes, setProbes] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)();\n    const [efSearch, setEfSearch] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)();\n    const [fullTextWeight, setFullTextWeight] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)();\n    const [semanticWeight, setSemanticWeight] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)();\n    const [fullTextLimit, setFullTextLimit] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)();\n    const [rrfK, setRrfK] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)();\n    const [kgSearchLevel, setKgSearchLevel] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [maxCommunityDescriptionLength, setMaxCommunityDescriptionLength] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(200);\n    const [localSearchLimits, setLocalSearchLimits] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const [temperature, setTemperature] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0.1);\n    const [topP, setTopP] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1);\n    const [topK, setTopK] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(100);\n    const [maxTokensToSample, setMaxTokensToSample] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1024);\n    const contentAreaRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const { switches, initializeSwitch, updateSwitch } = (0,_components_ChatDemo_SwitchManager__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    // Redirect to login if not authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (!isAuthenticated) {\n            router.push(\"/auth/login\");\n        }\n    }, [\n        isAuthenticated,\n        router\n    ]);\n    // Load configuration and initialize switches\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const initializeApp = async ()=>{\n            try {\n                const config = await (0,_config_chatConfig__WEBPACK_IMPORTED_MODULE_10__.loadChatConfig)();\n                // Set configuration values\n                setSearchLimit(config.vectorSearch.searchLimit);\n                setSearchFilters(config.vectorSearch.searchFilters);\n                setIndexMeasure(config.vectorSearch.indexMeasure);\n                setIncludeMetadatas(config.vectorSearch.includeMetadatas);\n                setProbes(config.vectorSearch.probes);\n                setEfSearch(config.vectorSearch.efSearch);\n                setFullTextWeight(config.hybridSearch.fullTextWeight);\n                setSemanticWeight(config.hybridSearch.semanticWeight);\n                setFullTextLimit(config.hybridSearch.fullTextLimit);\n                setRrfK(config.hybridSearch.rrfK);\n                setKgSearchLevel(config.graphSearch.kgSearchLevel);\n                setMaxCommunityDescriptionLength(config.graphSearch.maxCommunityDescriptionLength);\n                setLocalSearchLimits(config.graphSearch.localSearchLimits || {});\n                setTemperature(config.ragGeneration.temperature);\n                setTopP(config.ragGeneration.topP);\n                setTopK(config.ragGeneration.topK);\n                setMaxTokensToSample(config.ragGeneration.maxTokensToSample);\n                // Initialize switches based on configuration\n                initializeSwitch(\"vectorSearch\", config.vectorSearch.enabled, \"Vector Search\", \"Enable vector-based semantic search\");\n                initializeSwitch(\"hybridSearch\", config.hybridSearch.enabled, \"Hybrid Search\", \"Combine vector and full-text search\");\n                initializeSwitch(\"kgSearch\", config.graphSearch.enabled, \"Knowledge Graph Search\", \"Search using knowledge graph\");\n                initializeSwitch(\"includeMetadatas\", config.vectorSearch.includeMetadatas, \"Include Metadata\", \"Include document metadata in results\");\n                // Initialize Live2D character - always load the default character like livechat does\n                console.log(\"[ChatPage] Loading initial character:\", character);\n                setLive2dCharacter(character);\n            } catch (error) {\n                console.error(\"Failed to load configuration:\", error);\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        if (isAuthenticated) {\n            initializeApp();\n        }\n    }, [\n        isAuthenticated,\n        initializeSwitch\n    ]);\n    // Load collections\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const fetchCollections = async ()=>{\n            try {\n                const client = getClient();\n                if (client) {\n                    const response = await client.collections.list();\n                    setCollections(response.results || []);\n                }\n            } catch (error) {\n                console.error(\"Error fetching collections:\", error);\n            }\n        };\n        if (isAuthenticated && !isLoading) {\n            fetchCollections();\n        }\n    }, [\n        isAuthenticated,\n        isLoading,\n        getClient\n    ]);\n    // Handle query from URL parameters\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (router.query.q && typeof router.query.q === \"string\") {\n            setQuery(decodeURIComponent(router.query.q));\n        }\n    }, [\n        router.query.q\n    ]);\n    const toggleSidebar = ()=>{\n        setSidebarIsOpen(!sidebarIsOpen);\n    };\n    const handleSwitchChange = (id, checked)=>{\n        updateSwitch(id, checked);\n    };\n    const handleAbortRequest = ()=>{\n        // This will be handled by the Result component\n        console.log(\"Abort request triggered\");\n    };\n    const handleConversationSelect = async (conversationId)=>{\n        setSelectedConversationId(conversationId);\n        try {\n            const client = getClient();\n            if (!client) {\n                throw new Error(\"Failed to get authenticated client\");\n            }\n            const response = await client.conversations.retrieve({\n                id: conversationId\n            });\n            const fetchedMessages = response.results.map((message)=>{\n                var _message_metadata, _message_metadata1, _message_metadata2;\n                return {\n                    id: message.id,\n                    role: ((_message_metadata = message.metadata) === null || _message_metadata === void 0 ? void 0 : _message_metadata.role) || \"user\",\n                    content: ((_message_metadata1 = message.metadata) === null || _message_metadata1 === void 0 ? void 0 : _message_metadata1.content) || \"\",\n                    timestamp: ((_message_metadata2 = message.metadata) === null || _message_metadata2 === void 0 ? void 0 : _message_metadata2.timestamp) || Date.now()\n                };\n            });\n            setMessages(fetchedMessages);\n        } catch (error) {\n            console.error(\"Error fetching conversation:\", error);\n        }\n    };\n    if (!isAuthenticated) {\n        return null; // Will redirect to login\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        pageTitle: \"Chat\",\n        includeFooter: false,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-[calc(100vh-4rem)] overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Sidebar__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    isOpen: sidebarIsOpen,\n                    onToggle: toggleSidebar,\n                    switches: switches,\n                    handleSwitchChange: handleSwitchChange,\n                    searchLimit: searchLimit,\n                    setSearchLimit: setSearchLimit,\n                    searchFilters: searchFilters,\n                    setSearchFilters: setSearchFilters,\n                    collections: collections,\n                    selectedCollectionIds: selectedCollectionIds,\n                    setSelectedCollectionIds: setSelectedCollectionIds,\n                    indexMeasure: indexMeasure,\n                    setIndexMeasure: setIndexMeasure,\n                    includeMetadatas: includeMetadatas,\n                    setIncludeMetadatas: setIncludeMetadatas,\n                    probes: probes,\n                    setProbes: setProbes,\n                    efSearch: efSearch,\n                    setEfSearch: setEfSearch,\n                    fullTextWeight: fullTextWeight,\n                    setFullTextWeight: setFullTextWeight,\n                    semanticWeight: semanticWeight,\n                    setSemanticWeight: setSemanticWeight,\n                    fullTextLimit: fullTextLimit,\n                    setFullTextLimit: setFullTextLimit,\n                    rrfK: rrfK,\n                    setRrfK: setRrfK,\n                    kgSearchLevel: kgSearchLevel,\n                    setKgSearchLevel: setKgSearchLevel,\n                    maxCommunityDescriptionLength: maxCommunityDescriptionLength,\n                    setMaxCommunityDescriptionLength: setMaxCommunityDescriptionLength,\n                    localSearchLimits: localSearchLimits,\n                    setLocalSearchLimits: setLocalSearchLimits,\n                    temperature: temperature,\n                    setTemperature: setTemperature,\n                    topP: topP,\n                    setTopP: setTopP,\n                    topK: topK,\n                    setTopK: setTopK,\n                    maxTokensToSample: maxTokensToSample,\n                    setMaxTokensToSample: setMaxTokensToSample,\n                    onConversationSelect: handleConversationSelect\n                }, void 0, false, {\n                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\vividchat\\\\src\\\\pages\\\\chat.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex flex-col transition-all duration-300 \".concat(sidebarIsOpen ? \"ml-80\" : \"ml-0\"),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex flex-col overflow-hidden\",\n                        ref: contentAreaRef,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 flex justify-center overflow-hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full max-w-4xl flex flex-col overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 overflow-auto p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatDemo_result__WEBPACK_IMPORTED_MODULE_3__.Result, {\n                                            query: query,\n                                            setQuery: setQuery,\n                                            model: selectedModel,\n                                            userId: userId,\n                                            pipelineUrl: (pipeline === null || pipeline === void 0 ? void 0 : pipeline.deploymentUrl) || \"\",\n                                            searchLimit: searchLimit,\n                                            searchFilters: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.safeJsonParse)(searchFilters),\n                                            ragTemperature: temperature,\n                                            ragTopP: topP,\n                                            ragTopK: topK,\n                                            ragMaxTokensToSample: maxTokensToSample,\n                                            uploadedDocuments: uploadedDocuments,\n                                            setUploadedDocuments: setUploadedDocuments,\n                                            switches: switches,\n                                            hasAttemptedFetch: hasAttemptedFetch,\n                                            mode: mode,\n                                            selectedCollectionIds: selectedCollectionIds,\n                                            onAbortRequest: handleAbortRequest,\n                                            messages: messages,\n                                            setMessages: setMessages,\n                                            selectedConversationId: selectedConversationId,\n                                            setSelectedConversationId: setSelectedConversationId,\n                                            getClient: getClient\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\vividchat\\\\src\\\\pages\\\\chat.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\vividchat\\\\src\\\\pages\\\\chat.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4 border-t border-border\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatDemo_search__WEBPACK_IMPORTED_MODULE_4__.Search, {\n                                            pipeline: pipeline || undefined,\n                                            setQuery: setQuery,\n                                            placeholder: \"Start a conversation...\",\n                                            disabled: false\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\vividchat\\\\src\\\\pages\\\\chat.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\vividchat\\\\src\\\\pages\\\\chat.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\vividchat\\\\src\\\\pages\\\\chat.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\vividchat\\\\src\\\\pages\\\\chat.tsx\",\n                            lineNumber: 236,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\vividchat\\\\src\\\\pages\\\\chat.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\vividchat\\\\src\\\\pages\\\\chat.tsx\",\n                    lineNumber: 234,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Live2D__WEBPACK_IMPORTED_MODULE_8__.Live2D, {}, void 0, false, {\n                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\vividchat\\\\src\\\\pages\\\\chat.tsx\",\n                    lineNumber: 282,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\vividchat\\\\src\\\\pages\\\\chat.tsx\",\n            lineNumber: 187,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\vividchat\\\\src\\\\pages\\\\chat.tsx\",\n        lineNumber: 186,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ChatPage, \"frFK5bS3WYr+7TT3o7ChwSn0TgM=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter,\n        _context_UserContext__WEBPACK_IMPORTED_MODULE_9__.useUserContext,\n        _hooks_useLive2D__WEBPACK_IMPORTED_MODULE_12__.useLive2D,\n        _lib_store_character__WEBPACK_IMPORTED_MODULE_13__.useCharacterStore,\n        _components_ChatDemo_SwitchManager__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    ];\n});\n_c = ChatPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ChatPage);\nvar _c;\n$RefreshReg$(_c, \"ChatPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/chat.tsx\n"));

/***/ })

});