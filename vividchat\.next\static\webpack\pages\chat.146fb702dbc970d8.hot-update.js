"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/chat",{

/***/ "./src/hooks/useLive2D.ts":
/*!********************************!*\
  !*** ./src/hooks/useLive2D.ts ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLive2D: function() { return /* binding */ useLive2D; }\n/* harmony export */ });\n/* harmony import */ var _lib_live2d_live2dManager__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/live2d/live2dManager */ \"./lib/live2d/live2dManager.ts\");\n/* harmony import */ var _lib_store_sentio__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/store/sentio */ \"./lib/store/sentio.ts\");\n\n\nconst useLive2D = ()=>{\n    const { ready, setReady } = (0,_lib_store_sentio__WEBPACK_IMPORTED_MODULE_1__.useSentioLive2DStore)();\n    const checkLive2DReady = ()=>{\n        let attempts = 0;\n        const maxAttempts = 10; // 最多等待10秒\n        const checkReady = ()=>{\n            attempts++;\n            console.log(\"[useLive2D] Checking ready state, attempt \".concat(attempts, \"/\").concat(maxAttempts));\n            if (_lib_live2d_live2dManager__WEBPACK_IMPORTED_MODULE_0__.Live2dManager.getInstance().isReady()) {\n                console.log(\"[useLive2D] Live2D is ready!\");\n                setReady(true);\n            } else if (attempts >= maxAttempts) {\n                console.warn(\"[useLive2D] Timeout waiting for Live2D to be ready, forcing ready state\");\n                setReady(true); // 强制设置为ready，避免永久阻塞\n            } else {\n                setTimeout(checkReady, 1000);\n            }\n        };\n        checkReady();\n    };\n    const setLive2dCharacter = (character)=>{\n        console.log(\"[useLive2D] setLive2dCharacter called with:\", character);\n        _lib_live2d_live2dManager__WEBPACK_IMPORTED_MODULE_0__.Live2dManager.getInstance().changeCharacter(character);\n        if (character != null) {\n            setReady(false);\n            checkLive2DReady();\n        }\n    };\n    return {\n        setLive2dCharacter,\n        ready\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvaG9va3MvdXNlTGl2ZTJELnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUEyRDtBQUNEO0FBR25ELE1BQU1FLFlBQVk7SUFDckIsTUFBTSxFQUFFQyxLQUFLLEVBQUVDLFFBQVEsRUFBRSxHQUFHSCx1RUFBb0JBO0lBRWhELE1BQU1JLG1CQUFtQjtRQUNyQixJQUFJQyxXQUFXO1FBQ2YsTUFBTUMsY0FBYyxJQUFJLFVBQVU7UUFFbEMsTUFBTUMsYUFBYTtZQUNmRjtZQUNBRyxRQUFRQyxHQUFHLENBQUMsNkNBQXlESCxPQUFaRCxVQUFTLEtBQWUsT0FBWkM7WUFFckUsSUFBSVAsb0VBQWFBLENBQUNXLFdBQVcsR0FBR0MsT0FBTyxJQUFJO2dCQUN2Q0gsUUFBUUMsR0FBRyxDQUFDO2dCQUNaTixTQUFTO1lBQ2IsT0FBTyxJQUFJRSxZQUFZQyxhQUFhO2dCQUNoQ0UsUUFBUUksSUFBSSxDQUFDO2dCQUNiVCxTQUFTLE9BQU8sb0JBQW9CO1lBQ3hDLE9BQU87Z0JBQ0hVLFdBQVdOLFlBQVk7WUFDM0I7UUFDSjtRQUVBQTtJQUNKO0lBRUEsTUFBTU8scUJBQXFCLENBQUNDO1FBQ3hCUCxRQUFRQyxHQUFHLENBQUMsK0NBQStDTTtRQUMzRGhCLG9FQUFhQSxDQUFDVyxXQUFXLEdBQUdNLGVBQWUsQ0FBQ0Q7UUFDNUMsSUFBSUEsYUFBYSxNQUFNO1lBQ25CWixTQUFTO1lBQ1RDO1FBQ0o7SUFDSjtJQUVBLE9BQU87UUFDSFU7UUFDQVo7SUFDSjtBQUNKLEVBQUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2hvb2tzL3VzZUxpdmUyRC50cz83OWNiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IExpdmUyZE1hbmFnZXIgfSBmcm9tICdAL2xpYi9saXZlMmQvbGl2ZTJkTWFuYWdlcic7XG5pbXBvcnQgeyB1c2VTZW50aW9MaXZlMkRTdG9yZSB9IGZyb20gJ0AvbGliL3N0b3JlL3NlbnRpbyc7XG5pbXBvcnQgeyBSZXNvdXJjZU1vZGVsIH0gZnJvbSAnQC9saWIvcHJvdG9jb2wnO1xuXG5leHBvcnQgY29uc3QgdXNlTGl2ZTJEID0gKCkgPT4ge1xuICAgIGNvbnN0IHsgcmVhZHksIHNldFJlYWR5IH0gPSB1c2VTZW50aW9MaXZlMkRTdG9yZSgpO1xuXG4gICAgY29uc3QgY2hlY2tMaXZlMkRSZWFkeSA9ICgpID0+IHtcbiAgICAgICAgbGV0IGF0dGVtcHRzID0gMDtcbiAgICAgICAgY29uc3QgbWF4QXR0ZW1wdHMgPSAxMDsgLy8g5pyA5aSa562J5b6FMTDnp5JcblxuICAgICAgICBjb25zdCBjaGVja1JlYWR5ID0gKCkgPT4ge1xuICAgICAgICAgICAgYXR0ZW1wdHMrKztcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKGBbdXNlTGl2ZTJEXSBDaGVja2luZyByZWFkeSBzdGF0ZSwgYXR0ZW1wdCAke2F0dGVtcHRzfS8ke21heEF0dGVtcHRzfWApO1xuXG4gICAgICAgICAgICBpZiAoTGl2ZTJkTWFuYWdlci5nZXRJbnN0YW5jZSgpLmlzUmVhZHkoKSkge1xuICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCdbdXNlTGl2ZTJEXSBMaXZlMkQgaXMgcmVhZHkhJyk7XG4gICAgICAgICAgICAgICAgc2V0UmVhZHkodHJ1ZSk7XG4gICAgICAgICAgICB9IGVsc2UgaWYgKGF0dGVtcHRzID49IG1heEF0dGVtcHRzKSB7XG4gICAgICAgICAgICAgICAgY29uc29sZS53YXJuKCdbdXNlTGl2ZTJEXSBUaW1lb3V0IHdhaXRpbmcgZm9yIExpdmUyRCB0byBiZSByZWFkeSwgZm9yY2luZyByZWFkeSBzdGF0ZScpO1xuICAgICAgICAgICAgICAgIHNldFJlYWR5KHRydWUpOyAvLyDlvLrliLborr7nva7kuLpyZWFkee+8jOmBv+WFjeawuOS5hemYu+WhnlxuICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICBzZXRUaW1lb3V0KGNoZWNrUmVhZHksIDEwMDApO1xuICAgICAgICAgICAgfVxuICAgICAgICB9O1xuXG4gICAgICAgIGNoZWNrUmVhZHkoKTtcbiAgICB9XG5cbiAgICBjb25zdCBzZXRMaXZlMmRDaGFyYWN0ZXIgPSAoY2hhcmFjdGVyOiBSZXNvdXJjZU1vZGVsIHwgbnVsbCkgPT4ge1xuICAgICAgICBjb25zb2xlLmxvZygnW3VzZUxpdmUyRF0gc2V0TGl2ZTJkQ2hhcmFjdGVyIGNhbGxlZCB3aXRoOicsIGNoYXJhY3Rlcik7XG4gICAgICAgIExpdmUyZE1hbmFnZXIuZ2V0SW5zdGFuY2UoKS5jaGFuZ2VDaGFyYWN0ZXIoY2hhcmFjdGVyKTtcbiAgICAgICAgaWYgKGNoYXJhY3RlciAhPSBudWxsKSB7XG4gICAgICAgICAgICBzZXRSZWFkeShmYWxzZSk7XG4gICAgICAgICAgICBjaGVja0xpdmUyRFJlYWR5KCk7XG4gICAgICAgIH1cbiAgICB9O1xuXG4gICAgcmV0dXJuIHtcbiAgICAgICAgc2V0TGl2ZTJkQ2hhcmFjdGVyLFxuICAgICAgICByZWFkeSxcbiAgICB9O1xufTtcbiJdLCJuYW1lcyI6WyJMaXZlMmRNYW5hZ2VyIiwidXNlU2VudGlvTGl2ZTJEU3RvcmUiLCJ1c2VMaXZlMkQiLCJyZWFkeSIsInNldFJlYWR5IiwiY2hlY2tMaXZlMkRSZWFkeSIsImF0dGVtcHRzIiwibWF4QXR0ZW1wdHMiLCJjaGVja1JlYWR5IiwiY29uc29sZSIsImxvZyIsImdldEluc3RhbmNlIiwiaXNSZWFkeSIsIndhcm4iLCJzZXRUaW1lb3V0Iiwic2V0TGl2ZTJkQ2hhcmFjdGVyIiwiY2hhcmFjdGVyIiwiY2hhbmdlQ2hhcmFjdGVyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/hooks/useLive2D.ts\n"));

/***/ })

});