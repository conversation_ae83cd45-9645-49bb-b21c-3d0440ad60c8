import { Live2dManager } from '@/lib/live2d/live2dManager';
import { useSentioLive2DStore } from '@/lib/store/sentio';
import { ResourceModel } from '@/lib/protocol';

export const useLive2D = () => {
    const { ready, setReady } = useSentioLive2DStore();

    const checkLive2DReady = () => {
        let attempts = 0;
        const maxAttempts = 10; // 最多等待10秒

        const checkReady = () => {
            attempts++;
            console.log(`[useLive2D] Checking ready state, attempt ${attempts}/${maxAttempts}`);

            if (Live2dManager.getInstance().isReady()) {
                console.log('[useLive2D] Live2D is ready!');
                setReady(true);
            } else if (attempts >= maxAttempts) {
                console.warn('[useLive2D] Timeout waiting for Live2D to be ready, forcing ready state');
                setReady(true); // 强制设置为ready，避免永久阻塞
            } else {
                setTimeout(checkReady, 1000);
            }
        };

        checkReady();
    }

    const setLive2dCharacter = (character: ResourceModel | null) => {
        console.log('[useLive2D] setLive2dCharacter called with:', character);
        console.log('[useLive2D] Current ready state before change:', ready);

        try {
            Live2dManager.getInstance().changeCharacter(character);
            if (character != null) {
                console.log('[useLive2D] Setting ready to false and starting check');
                setReady(false);
                checkLive2DReady();
            } else {
                console.log('[useLive2D] Character is null, setting ready to true');
                setReady(true);
            }
        } catch (error) {
            console.error('[useLive2D] Error changing character:', error);
            setReady(true); // 出错时也设置为ready，避免阻塞
        }
    };

    return {
        setLive2dCharacter,
        ready,
    };
};
