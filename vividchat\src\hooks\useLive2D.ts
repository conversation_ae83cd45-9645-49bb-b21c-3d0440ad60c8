import { useCallback } from 'react';
import { Live2dManager } from '@/lib/live2d/live2dManager';
import { useSentioLive2DStore } from '@/lib/store/sentio';
import { ResourceModel } from '@/lib/protocol';

export const useLive2D = () => {
    const { ready, setReady } = useSentioLive2DStore();

    const checkLive2DReady = useCallback(() => {
        const checkReady = () => {
            if (Live2dManager.getInstance().isReady()) {
                console.log('[useLive2D] Live2D is ready, setting ready state to true');
                setReady(true);
            } else {
                console.log('[useLive2D] Live2D not ready yet, checking again in 500ms');
                setTimeout(checkReady, 500);
            }
        };
        checkReady();
    }, [setReady]);

    const setLive2dCharacter = useCallback((character: ResourceModel | null) => {
        console.log('[useLive2D] setLive2dCharacter called with:', character);
        Live2dManager.getInstance().change<PERSON>haracter(character);
        if (character != null) {
            console.log('[useLive2D] Setting ready to false and starting check');
            setReady(false);
            checkLive2DReady();
        }
    }, [setReady, checkLive2DReady]);

    return {
        setLive2dCharacter,
        ready,
    };
};
